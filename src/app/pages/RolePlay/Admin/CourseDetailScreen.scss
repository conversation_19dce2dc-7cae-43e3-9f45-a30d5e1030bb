.course-detail-container {
  .course-detail-header-card {
    margin-bottom: 24px;

    .course-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .course-detail-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .course-detail-description {
        margin: 4px 0 0;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  .course-detail-form-card {
    .ant-card-head-title {
      font-weight: 600;
    }

    .ant-upload-picture-card-wrapper {
      // display: inline-block;
    }

    .ant-upload.ant-upload-select-picture-card {
      width: 104px;
      height: 104px;
      margin-right: 8px;
      margin-bottom: 8px;
      background-color: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 2px;
      cursor: pointer;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }

      .ant-upload-disabled {
        cursor: not-allowed;
      }
    }

    .ant-upload-list-picture-card .ant-upload-list-item {
      width: 104px;
      height: 104px;
      padding: 0;
    }

    .ant-upload-list-item-info {
      > span {
        display: block;
        width: 100%;
        height: 100%;
      }

      img {
        object-fit: cover;
      }
    }

  }

  .btn-cancel {
    // Custom styles if needed
  }

  .btn-save {
    min-width: 100px;
  }
}

// Professional card layout for reference materials
.reference-materials-section {
  .reference-empty-state {
    text-align: center;
    padding: 60px 20px;

    .ant-empty {
      margin: 0;

      .ant-empty-description {
        color: #8c8c8c;
        font-size: 14px;
        margin-bottom: 16px;
      }
    }
  }

  // Controls header
  .reference-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    .reference-info {
      .reference-count {
        color: #595959;
        font-size: 14px;
      }
    }

    .reference-view-controls {
      .ant-btn-group {
        .ant-btn {
          // Minimal fix - chỉ đảm bảo visibility
          &:not(.ant-btn-primary) {
            border: 1px solid #d9d9d9;
            background-color: #ffffff;
            color: rgba(0, 0, 0, 0.65);
          }

          &:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }

          &:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }

          &:not(:first-child) {
            margin-left: -1px;
          }
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }


}

  // Horizontal scroll container for cards
  .reference-cards-scroll-container {
    position: relative;
    margin-top: 8px;

    .reference-cards-scroll {
      display: flex;
      gap: 16px;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 8px 0 16px 0;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;

      // Custom scrollbar styling
      &::-webkit-scrollbar {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: #a8a8a8;
        }
      }

      // Firefox scrollbar
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;

      @media (max-width: 768px) {
        gap: 12px;
        padding: 8px 0 12px 0;

        &::-webkit-scrollbar {
          height: 4px;
        }
      }
    }
  }






  // List View Styling - Professional Design
  .reference-list-view {
    .reference-list-item {
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 16px;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 6px 20px rgba(24, 144, 255, 0.12);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }

      .reference-list-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        border: 1px solid #d9f0ff;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        color: #1890ff;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .reference-list-icon {
        margin-right: 16px;
        font-size: 28px;
        color: #1890ff;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: #f8faff;
        border-radius: 10px;
        border: 1px solid #e6f7ff;
      }

      .reference-list-content {
        flex: 1;
        min-width: 0;

        .reference-list-header {
          margin-bottom: 12px;

          .reference-list-title {
            .reference-list-link {
              color: #262626;
              text-decoration: none;
              font-weight: 600;
              font-size: 16px;
              line-height: 1.4;
              display: block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              transition: color 0.2s ease;

              &:hover {
                color: #1890ff;
              }
            }
          }
        }

        .reference-list-meta {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .reference-list-date {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #8c8c8c;
            font-size: 13px;
            font-weight: 400;

            .anticon {
              font-size: 12px;
              color: #bfbfbf;
            }
          }

          .reference-list-badges {
            display: flex;
            gap: 8px;
            flex-shrink: 0;

            .reference-type-tag {
              font-weight: 600;
              border-radius: 8px;
              font-size: 12px;
              padding: 4px 12px;
              border: none;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &.ant-tag-blue {
                background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
                color: #ffffff;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
              }

              &.ant-tag-green {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                color: #ffffff;
                box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
              }
            }

            .reference-visibility-tag {
              font-weight: 600;
              border-radius: 8px;
              font-size: 12px;
              padding: 4px 12px;
              border: none;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &.ant-tag-success {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                color: #ffffff;
                box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
              }

              &.ant-tag-warning {
                background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
                color: #ffffff;
                box-shadow: 0 2px 4px rgba(250, 173, 20, 0.3);
              }

              .anticon {
                margin-right: 4px;
                font-size: 10px;
              }
            }
          }
        }
      }

      // Action buttons styling is now handled by global action-buttons class in buttons.scss

      @media (max-width: 768px) {
        align-items: flex-start;
        gap: 12px;
        padding: 16px;

        .reference-list-number {
          margin-top: 4px;
        }

        .reference-list-icon {
          margin-top: 4px;
          width: 40px;
          height: 40px;
          font-size: 20px;
        }

        .reference-list-content {
          .reference-list-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .reference-list-badges {
              align-self: flex-start;
            }
          }
        }

        .action-buttons {
          margin-top: 4px;
        }
      }

      @media (max-width: 480px) {
        padding: 12px;
        margin-bottom: 12px;
        flex-direction: column;
        align-items: flex-start;

        .reference-list-number,
        .reference-list-icon {
          margin-bottom: 8px;
        }

        .reference-list-content {
          width: 100%;

          .reference-list-header {
            .reference-list-title {
              .reference-list-link {
                font-size: 14px;
              }
            }
          }

          .reference-list-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }

        .action-buttons {
          align-self: flex-end;
          margin-top: 8px;
        }
      }
    }
  }

// Clean empty state styling
.ant-empty {
  padding: 40px 20px;

  .ant-empty-description {
    color: #8c8c8c;
    font-size: 14px;
  }
}

// Public checkbox styling
.reference-public-checkbox {
  .ant-checkbox-wrapper {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}

// Card header improvements
.ant-card-head {
  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .ant-card-extra {
    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
