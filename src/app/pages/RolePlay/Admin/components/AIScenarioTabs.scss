.ai-scenario-tabs {
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 24px;
      border-bottom: 1px solid #f0f0f0;

      .ant-tabs-tab {
        position: relative;
        margin: 0 8px 0 0;
        padding: 8px 16px;
        transition: all 0.2s ease;
        border-radius: 4px 4px 0 0;

        &:hover {
          color: #1890ff;
        }

        &.ant-tabs-tab-active {
          color: #1890ff;
          font-weight: 500;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #1890ff;
          }
        }

        .tab-label {
          display: flex;
          align-items: center;
          gap: 8px;
          max-width: 150px; // Cố định độ rộng tối đa cho tab

          span {
            cursor: pointer;
            max-width: 120px; // Giới hạn độ rộng của text
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }

          .ant-btn {
            opacity: 0;
            transition: opacity 0.2s;
            border: none;
            padding: 0;
            margin-left: 4px;

            &:hover {
              color: #1890ff;
              background: transparent;
            }
          }
        }

        &:hover .tab-label .ant-btn {
          opacity: 1;
        }

        .tab-label-editing {
          display: flex;
          align-items: center;
          gap: 8px;

          .ant-input {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
          }

          .ant-btn {
            opacity: 1;
            padding: 0;
            margin-left: 4px;
          }
        }
      }

      .ant-tabs-nav-add {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px dashed #d9d9d9;
        background: transparent;
        border-radius: 4px;
        transition: all 0.2s ease;
        margin-left: 8px;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }

    .ant-tabs-content-holder {
      .scenario-tab-content {
        padding: 24px 0;

        // Card styling for consistent design
        .ant-card {
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          margin-bottom: 24px;

          .ant-card-head {
            border-bottom: 1px solid #f0f0f0;

            .ant-card-head-title {
              padding: 16px 0;

              h5, .ant-typography-title {
                margin: 0;
                font-weight: 600;
                color: #262626;

                .anticon {
                  margin-right: 8px;
                  color: #1890ff;
                }
              }
            }
          }

          .ant-card-body {
            padding: 24px;
          }

          // Card specific styles
          &.basic-info-card {
            margin-bottom: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }

          &.ai-config-card {
            margin-bottom: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);

            .no-persona-message {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding: 60px 20px;
              text-align: center;
              background: #fafafa;
              border-radius: 8px;
              border: 2px dashed #d9d9d9;

              .no-persona-icon {
                font-size: 48px;
                color: #bfbfbf;
                margin-bottom: 16px;
              }

              .no-persona-text {
                p {
                  margin: 0;

                  &:first-child {
                    font-size: 16px;
                    font-weight: 600;
                    color: #595959;
                    margin-bottom: 8px;
                  }

                  &.no-persona-description {
                    font-size: 14px;
                    color: #8c8c8c;
                    line-height: 1.5;
                  }
                }
              }
            }
          }



          &.tasks-management-card {
            margin-bottom: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
        }

        // Form styling with AntForm - use global styles like CourseDetailScreen
        .ant-form {
          .ant-form-item {
            margin-bottom: 16px;

            // Use global label styling (font-weight: 600 from antd-form.scss)
            // No custom label styling needed - inherit from global

            // Use global input styling from theme/index.js
            // Only add specific customizations if needed
            .ant-input-number-group-addon {
              background-color: #fafafa;
              color: rgba(0, 0, 0, 0.65);
            }

            // TextArea specific styling
            .ant-input {
              &[rows] {
                min-height: 100px;
                resize: vertical;
              }
            }
          }
        }

        // Alert styling
        .ant-alert {
          border-radius: 8px;
          margin-bottom: 24px;
        }

        // Dropdown render styling
        .persona-dropdown-footer {
          padding: 8px;
          text-align: center;
          background: #f5f5f5;

          .ant-btn {
            width: 100%;
          }
        }

        // Tasks management section styling
        .tasks-header {
          display: flex;
          align-items: center;
          gap: 8px;

          .ant-typography-title {
            margin: 0;
            color: #262626;
            font-weight: 600;

            .anticon {
              margin-right: 8px;
              color: #1890ff;
            }
          }

          .ant-badge {
            .ant-badge-count {
              background-color: #52c41a;
            }
          }
        }
      }
    }
  }
}

// Default scenario highlighting
.ai-scenario-tabs .ant-tabs-tab {
  &.default-scenario {
    background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
    border-color: #1890ff;

    &.ant-tabs-tab-active {
      border-bottom-color: #1890ff;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .ai-scenario-tabs {
    .ant-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          .tab-label {
            max-width: 120px; // Giảm độ rộng trên tablet

            span {
              max-width: 100px; // Giảm độ rộng text trên tablet
            }

            .default-badge {
              display: none;
            }
          }
        }
      }

      .ant-tabs-content-holder {
        .scenario-tab-content {
          padding: 16px 0;

          .ant-card {
            margin-bottom: 16px;

            .ant-card-body {
              padding: 16px;
            }
          }

          .ant-row {
            .ant-col {
              margin-bottom: 16px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-scenario-tabs {
    .ant-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: 8px 12px;

          .tab-label {
            font-size: 12px;
            max-width: 80px; // Giảm độ rộng trên mobile

            span {
              max-width: 60px; // Giảm độ rộng text trên mobile
            }

            .ant-btn {
              display: none;
            }
          }

          .tab-label-editing {
            .ant-input {
              width: 80px;
              font-size: 12px;
            }

            .ant-btn {
              padding: 0;
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .ant-tabs-content-holder {
        .scenario-tab-content {
          padding: 12px 0;

          .ant-card {
            margin-bottom: 12px;
            border-radius: 6px;

            .ant-card-head {
              .ant-card-head-title {
                padding: 12px 0;

                h4 {
                  font-size: 16px;
                }
              }
            }

            .ant-card-body {
              padding: 16px;
            }

            .ant-card-extra {
              .ant-btn {
                font-size: 12px;
                padding: 4px 8px;
                height: auto;
              }
            }
          }

          .ant-form {
            .ant-form-item {
              margin-bottom: 12px;

              .ant-form-item-label {
                label {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Animation for tab transitions
.ai-scenario-tabs .ant-tabs-content {
  .ant-tabs-tabpane {
    transition: opacity 0.3s ease;

    &.ant-tabs-tabpane-active {
      opacity: 1;
    }

    &:not(.ant-tabs-tabpane-active) {
      opacity: 0;
    }
  }
}

// Custom styling for form elements within tabs - minimal overrides
.scenario-tab-content {
  // Use global form styling - no custom overrides needed
  // All styling should inherit from global antd-form.scss and theme/index.js

  .ant-checkbox-wrapper {
    font-weight: 500;

    .ant-checkbox {
      .ant-checkbox-inner {
        border-radius: 4px;
      }
    }
  }

  // AI Persona Preview styling
  .ai-persona-preview {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 20px;
    margin-top: 24px;

    .preview-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .preview-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #1890ff;
        font-size: 16px;
      }

      .preview-title {
        h5 {
          margin: 0;
          color: #262626;
          font-weight: 600;
        }

        .preview-subtitle {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}

// Loading state
.ai-scenario-tabs.loading {
  .ant-tabs-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // Pass Score Field Styling
  .ant-form-item {
    &:has([name="passScore"]) {
      .ant-input-number {
        .ant-input-number-input {
          text-align: center;
          font-weight: 600;
          color: #1890ff;
        }

        &:hover {
          border-color: #40a9ff;
        }

        &:focus-within {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}
