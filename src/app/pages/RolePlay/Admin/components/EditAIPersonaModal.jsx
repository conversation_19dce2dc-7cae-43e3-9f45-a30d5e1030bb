import React, {useState, useEffect} from 'react';
import {Modal, Button, Form, Input, InputNumber, Select, Tooltip, Row, Col} from 'antd';
import {InfoCircleOutlined} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';

import {updateAIPersona} from '@services/RolePlay/AIPersonaService';
import {uploadFile} from '@services/File';
import {toast} from '@component/ToastProvider';
import UploadImagePreview from '@component/UploadImagePreview';
import {AntForm} from '@component/AntForm';
import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";

const EditAIPersonaModal = ({isOpen, onClose, onSuccess, persona}) => {
  const {t} = useTranslation();
  const [personaForm] = Form.useForm();
  const [isUpdatingAIPersona, setIsUpdatingAIPersona] = useState(false);
  const [avatarId, setAvatarId] = useState(null);
  const [isLoadingAvatar, setIsLoadingAvatar] = useState(false);

  // Initialize form when persona changes
  useEffect(() => {
    if (persona && isOpen) {
      personaForm.setFieldsValue({
        name: persona.name || '',
        age: persona.age || '',
        gender: persona.gender || '',
        role: persona.role || '',
        mood: persona.mood || '',
        organization: persona.organization || '',
        personaBackground: persona.personaBackground || '',
        personaConcern: persona.personaConcern || '',
        smallTalkLikely: persona.smallTalkLikely || 0,
        voice: persona.voice || '',
        avatarId: persona.avatarId || null,
      });
      setAvatarId(persona.avatarId || null);
    }
  }, [persona, isOpen, personaForm]);

  const handleUploadAvatar = async file => {
    setIsLoadingAvatar(true);
    try {
      const uploadResponse = await uploadFile(file, {folder: 'image'});
      if (uploadResponse && uploadResponse._id) {
        setAvatarId(uploadResponse._id);
        personaForm.setFieldsValue({avatarId: uploadResponse._id});
        toast.success(t('UPLOAD_AVATAR_SUCCESS'));
      } else {
        toast.error(t('UPLOAD_AVATAR_ERROR'));
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      toast.error(t('UPLOAD_AVATAR_ERROR'));
    } finally {
      setIsLoadingAvatar(false);
    }
  };

  const handleClearAvatar = () => {
    setAvatarId(null);
    personaForm.setFieldsValue({avatarId: null});
  };

  const handleUpdateAIPersona = async () => {
    setIsUpdatingAIPersona(true);
    try {
      const values = await personaForm.validateFields();
      const payloadToSend = {
        ...values,
        _id: persona._id,
        avatarId: avatarId,
      };

      const apiResponse = await updateAIPersona(payloadToSend);

      if (apiResponse) {
        handleCancel();

        // Gọi callback onSuccess với AI Persona vừa cập nhật
        if (onSuccess && typeof onSuccess === 'function') {
          onSuccess(apiResponse);
        }
      } else {
        toast.error(t('UPDATE_AI_PERSONA_ERROR'));
      }
    } catch (error) {
      console.error('Error updating AI Persona:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsUpdatingAIPersona(false);
    }
  };

  const handleCancel = () => {
    onClose();
    personaForm.resetFields();
    setAvatarId(null);
  };

  return (
    <Modal
      title={t('EDIT_AI_PERSONA')}
      open={isOpen}
      onCancel={handleCancel}
      width={800}
      footer={[
        <AntButton key="cancel" size={'large'} type={BUTTON.TEXT} onClick={handleCancel} disabled={isUpdatingAIPersona}>
          {t('CANCEL')}
        </AntButton>,
        <AntButton
          key="submit"
          type={BUTTON.DEEP_NAVY}
          onClick={handleUpdateAIPersona}
          loading={isUpdatingAIPersona}
          disabled={isUpdatingAIPersona || isLoadingAvatar}
          size={'large'}
        >
          {t('UPDATE')}
        </AntButton>,
      ]}
    >
      <AntForm form={personaForm} layout="vertical">
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="name"
              label={t('AI_PERSONA_NAME')}
              rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_NAME')})}]}
            >
              <Input placeholder={t('ENTER_AI_PERSONA_NAME')} />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item name="age" label={t('AGE')}>
              <Input placeholder={t('ENTER_AI_PERSONA_AGE')} />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item name="gender" label={t('GENDER')}>
              <Select placeholder={t('SELECT_GENDER')}>
                <Select.Option value="male">{t('MALE')}</Select.Option>
                <Select.Option value="female">{t('FEMALE')}</Select.Option>
                <Select.Option value="other">{t('OTHER')}</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="role"
              label={t('AI_PERSONA_ROLE')}
              rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_ROLE')})}]}
            >
              <Input placeholder={t('ENTER_AI_PERSONA_ROLE')} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item name="mood" label={t('AI_PERSONA_MOOD')}>
              <Input placeholder={t('ENTER_AI_PERSONA_MOOD')} />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item name="organization" label={t('AI_PERSONA_ORGANIZATION')}>
              <Input placeholder={t('ENTER_AI_PERSONA_ORGANIZATION')} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label={t('AI_PERSONA_AVATAR')}>
          <UploadImagePreview
            imageId={avatarId}
            onDrop={handleUploadAvatar}
            onClear={handleClearAvatar}
            loading={isLoadingAvatar}
          />
        </Form.Item>
        <Form.Item name="avatarId" hidden>
          <Input />
        </Form.Item>

        <Form.Item
          name="personaBackground"
          label={t('AI_PERSONA_BACKGROUND')}
          rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_BACKGROUND')})}]}
        >
          <Input.TextArea rows={4} placeholder={t('ENTER_AI_PERSONA_BACKGROUND')} />
        </Form.Item>

        <Form.Item name="personaConcern" label={t('AI_PERSONA_CONCERNS')}>
          <Input.TextArea rows={3} placeholder={t('ENTER_AI_PERSONA_CONCERNS')} />
        </Form.Item>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Form.Item
              name="smallTalkLikely"
              label={
                <span>
                  {t('AI_PERSONA_SMALL_TALK_LIKELY')}
                  <Tooltip title={t('AI_PERSONA_SMALL_TALK_LIKELY_TOOLTIP')}>
                    <InfoCircleOutlined style={{marginLeft: 4, color: 'rgba(0,0,0,.45)'}} />
                  </Tooltip>
                </span>
              }
              rules={[{type: 'number', min: 0, max: 100, message: t('VALUE_BETWEEN_0_100')}]}
            >
              <InputNumber min={0} max={100} placeholder="0-100" style={{width: '100%'}} />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="voice"
              label={t('AI_PERSONA_VOICE')}
            >
              <Input placeholder={t('ENTER_AI_PERSONA_VOICE')} />
            </Form.Item>
          </Col>
        </Row>
      </AntForm>
    </Modal>
  );
};

export default EditAIPersonaModal;
