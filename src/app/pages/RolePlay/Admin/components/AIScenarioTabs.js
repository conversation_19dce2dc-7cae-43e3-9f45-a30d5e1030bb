import React, {useState, useEffect, forwardRef, useImperativeHandle, useMemo, useCallback, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Tabs,
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Checkbox,
  Row,
  Col,
  Button,
  Space,
  Typography,
  message,
  Divider,
  Badge,
  Tooltip,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  UserOutlined,
  FileTextOutlined,
  SettingOutlined,
  RobotOutlined,
  EditOutlined,
} from '@ant-design/icons';

import {BUTTON} from '@constant';
import AntButton from '@component/AntButton';
import {AntForm} from '@component/AntForm';
import {toast} from '@component/ToastProvider';
import {confirm} from '@component/ConfirmProvider';

import {
  getScenariosByCourse,
  createAIScenario,
  updateAIScenario,
  deleteAIScenario,
} from '@services/RolePlay/AIScenarioService';

import {getAllAIPersonasWithoutPagination} from '@services/RolePlay/AIPersonaService';
import {getAllInstruction} from '@services/RolePlay/RolePlayInstructionService';
import {createRoleplayTask} from '@services/RolePlay/TaskService';

import {AIPersonaSetupCard} from '../../AIPersona/AIPersonaSetupCard';
import {TaskEvaluationDisplayCard} from '../../Tasks/TaskEvaluationDisplayCard';
import CreateAIPersonaModal from './CreateAIPersonaModal';
import EditAIPersonaModal from './EditAIPersonaModal';

import './AIScenarioTabs.scss';

const {TextArea} = Input;
const {Option} = Select;
const {Text} = Typography;

const AIScenarioTabs = forwardRef(({
                                     courseId,
                                     onScenariosChange,
                                     onTaskAdd,
                                     onTaskUpdate,
                                     onTaskDelete,
                                   }, ref) => {
  const {t} = useTranslation();
  const [form] = Form.useForm();

  const [scenarios, setScenarios] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState('');
  const [loading, setLoading] = useState(false);

  // Form data
  const [aiPersonas, setAiPersonas] = useState([]);
  const [roleplayInstructions, setRoleplayInstructions] = useState([]);
  const [selectedPersonaForPreview, setSelectedPersonaForPreview] = useState(null);

  // Modal states
  const [isCreatePersonaModalOpen, setIsCreatePersonaModalOpen] = useState(false);
  const [isEditPersonaModalOpen, setIsEditPersonaModalOpen] = useState(false);
  const [editingPersona, setEditingPersona] = useState(null);

  // Tab editing - loại bỏ vì không cho phép edit tên tab trực tiếp

  // Force re-render when tasks change
  const [taskUpdateTrigger, setTaskUpdateTrigger] = useState(0);

  // Track current simulation type from form
  const [currentSimulationType, setCurrentSimulationType] = useState(null);

  // Force re-render khi scenarios thay đổi
  useEffect(() => {
    setTaskUpdateTrigger(prev => prev + 1);
  }, [scenarios]);

  // Ref for TaskEvaluationDisplayCard to trigger AI modal
  const taskCardRef = useRef(null);

  useEffect(() => {
    if (courseId) {
      fetchFormData();
      fetchScenarios();
    }
  }, [courseId]);

  useEffect(() => {
    // Update form when active tab changes
    if (activeTabKey && scenarios.length > 0) {
      const currentScenario = scenarios.find(s => s._id === activeTabKey);
      if (currentScenario) {
        updateFormWithScenario(currentScenario);
      }
    }
  }, [activeTabKey, scenarios]);

  const fetchFormData = async () => {
    try {
      const [personasResponse, instructionsResponse] = await Promise.all([
        getAllAIPersonasWithoutPagination({}, [], false),
        getAllInstruction({}),
      ]);

      if (personasResponse) {
        setAiPersonas(personasResponse);
      }
      if (instructionsResponse) {
        setRoleplayInstructions(instructionsResponse);
      }
    } catch (error) {
      console.error('Error fetching form data:', error);
      toast.error(t('ERROR_FETCHING_FORM_DATA'));
    }
  };

  const fetchScenarios = async () => {
    setLoading(true);
    try {
      const response = await getScenariosByCourse(courseId, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
      if (response && response.length > 0) {
        setScenarios(response);
        // Set active tab to first scenario
        setActiveTabKey(response[0]._id);
        if (onScenariosChange) {
          onScenariosChange(response);
        }
      } else {
        // Create first empty scenario if none exist
        createFirstScenario();
      }
    } catch (error) {
      console.error('Error fetching scenarios:', error);
      createFirstScenario();
    } finally {
      setLoading(false);
    }
  };

  const createFirstScenario = () => {
    const newScenario = {
      _id: `temp_${Date.now()}`,
      name: t('SCENARIO') + ' 1',
      description: '',
      courseId,
      isNew: true,
      taskIds: [],
    };
    setScenarios([newScenario]);
    setActiveTabKey(newScenario._id);
    form.resetFields();
  };

  const updateFormWithScenario = (scenario) => {
    if (!scenario) return;

    const formValues = {
      name: scenario.name || '',
      description: scenario.description || '',
      simulationType: scenario.simulationType || '',
      roleplayInstructionId: scenario.roleplayInstructionId?._id || scenario.roleplayInstructionId,
      taskIds: scenario.taskIds?.map(task => typeof task === 'object' ? task._id : task) || [],
      estimatedCallTimeInMinutes: scenario.estimatedCallTimeInMinutes,
      passScore: scenario.passScore,
    };

    form.setFieldsValue(formValues);

    // Set current simulation type for filtering
    setCurrentSimulationType(scenario.simulationType || null);

    // Update selected persona for preview
    if (scenario.aiPersonaId) {
      if (typeof scenario.aiPersonaId === 'object') {
        setSelectedPersonaForPreview(scenario.aiPersonaId);
      } else {
        // Find persona by ID
        const persona = aiPersonas.find(p => p._id === scenario.aiPersonaId);
        setSelectedPersonaForPreview(persona || null);
      }
    } else {
      setSelectedPersonaForPreview(null);
    }
  };

  // Handle AI Persona creation success
  const handlePersonaCreated = (newPersona) => {
    setAiPersonas(prev => [...prev, newPersona]);
    setSelectedPersonaForPreview(newPersona); // Set persona mới làm preview
    setIsCreatePersonaModalOpen(false);

    // Cập nhật scenario hiện tại với AI persona mới và đánh dấu là modified
    const currentScenario = getCurrentScenario();
    if (currentScenario) {
      setScenarios(prev => prev.map(scenario =>
        scenario._id === currentScenario._id
          ? {
              ...scenario,
              aiPersonaId: newPersona._id,
              isModified: true
            }
          : scenario
      ));
    }

    // Không hiển thị toast ở đây vì modal đã hiển thị rồi
  };

  // Handle AI Persona edit
  const handlePersonaEdit = (persona) => {
    setEditingPersona(persona);
    setIsEditPersonaModalOpen(true);
  };

  // Handle AI Persona update success
  const handlePersonaUpdated = (updatedPersona) => {
    setAiPersonas(prev => prev.map(p => p._id === updatedPersona._id ? updatedPersona : p));
    setIsEditPersonaModalOpen(false);
    setEditingPersona(null);

    // Update selected persona for preview if it's the same one
    if (selectedPersonaForPreview && selectedPersonaForPreview._id === updatedPersona._id) {
      setSelectedPersonaForPreview(updatedPersona);

      // Cập nhật scenario hiện tại với AI persona đã update và đánh dấu là modified
      const currentScenario = getCurrentScenario();
      if (currentScenario && currentScenario.aiPersonaId === updatedPersona._id) {
        setScenarios(prev => prev.map(scenario =>
          scenario._id === currentScenario._id
            ? {
                ...scenario,
                aiPersonaId: updatedPersona._id,
                isModified: true
              }
            : scenario
        ));
      }
    }

    toast.success(t('AI_PERSONA_UPDATED_SUCCESS'));
  };

  const updateCurrentScenario = (updates) => {
    setScenarios(prev => {
      return prev.map(scenario => {
        if (scenario._id === activeTabKey) {
          // Đảm bảo aiPersonaId được giữ từ selectedPersonaForPreview nếu có
          const updatedScenario = {
            ...scenario,
            ...updates,
            isModified: true
          };

          // Nếu có selectedPersonaForPreview và chưa có aiPersonaId trong updates, thì set từ selectedPersonaForPreview
          if (selectedPersonaForPreview && !updates.aiPersonaId && !scenario.aiPersonaId) {
            updatedScenario.aiPersonaId = selectedPersonaForPreview._id;
          }

          return updatedScenario;
        }
        return scenario;
      });
    });
  };

  const handleAddScenario = () => {
    const newScenario = {
      _id: `temp_${Date.now()}`,
      name: `${t('SCENARIO')} ${scenarios.length + 1}`,
      description: '',
      courseId,
      isNew: true,
      taskIds: [],
    };

    setScenarios(prev => [...prev, newScenario]);
    setActiveTabKey(newScenario._id);
  };

  const handleTabEdit = (targetKey, action) => {
    if (action === 'add') {
      handleAddScenario();
    } else if (action === 'remove') {
      handleDeleteScenario(targetKey);
    }
  };

  const handleDeleteScenario = (scenarioId) => {
    const scenario = scenarios.find(s => s._id === scenarioId);
    if (!scenario) {
      return;
    }

    // Không cho phép xóa nếu chỉ còn 1 scenario
    if (scenarios.length <= 1) {
      toast.error(t('CANNOT_DELETE_LAST_SCENARIO', 'Không thể xóa kịch bản cuối cùng'));
      return;
    }

    // Nếu là scenario mới (chưa lưu), xóa trực tiếp
    if (scenario.isNew) {
      const updatedScenarios = scenarios.filter(s => s._id !== scenarioId);
      setScenarios(updatedScenarios);

      // Chuyển sang tab khác nếu đang ở tab bị xóa
      if (activeTabKey === scenarioId) {
        setActiveTabKey(updatedScenarios[0]._id);
      }
      return;
    }

    // Hiển thị confirmation modal cho scenario đã lưu
    confirm.delete({
      title: t('CONFIRM_DELETE_SCENARIO', 'Xác nhận xóa kịch bản'),
      content: t('DELETE_SCENARIO_CONFIRMATION', 'Bạn có chắc chắn muốn xóa kịch bản "{name}"? Hành động này không thể hoàn tác.', { name: scenario.name }),
      okText: t('DELETE', 'Xóa'),
      cancelText: t('CANCEL', 'Hủy'),
      handleConfirm: async () => {
        try {
          await deleteAIScenario(scenarioId, false);
          toast.success(t('DELETE_SCENARIO_SUCCESS', 'Xóa kịch bản thành công'));

          // Cập nhật danh sách scenarios
          const updatedScenarios = scenarios.filter(s => s._id !== scenarioId);
          setScenarios(updatedScenarios);

          // Chuyển sang tab khác nếu đang ở tab bị xóa
          if (activeTabKey === scenarioId) {
            setActiveTabKey(updatedScenarios[0]._id);
          }

          // Thông báo cho parent component
          if (onScenariosChange) {
            onScenariosChange(updatedScenarios);
          }
        } catch (error) {
          console.error('Error deleting scenario:', error);
          toast.error(t('DELETE_SCENARIO_ERROR', 'Có lỗi xảy ra khi xóa kịch bản'));
        }
      },
    });
  };

  // Loại bỏ các function edit tab name vì không cho phép edit trực tiếp


  // Check if there are unsaved tasks
  const checkUnsavedTasks = useCallback(() => {
    const currentScenario = scenarios.find(s => s._id === activeTabKey);
    if (!currentScenario || !currentScenario.taskIds) return [];

    // Vì hệ thống đã tự động lưu task từ AI, chúng ta chỉ cần kiểm tra
    // các task được tạo thủ công và chưa được lưu
    const unsavedTasks = currentScenario.taskIds.filter(task => {
      if (typeof task === 'string') {
        // String ID luôn được coi là đã lưu
        return false;
      }

      if (typeof task === 'object' && task !== null) {
        const taskId = task._id;

        // Chỉ coi là "unsaved" nếu:
        // 1. Không có ID
        // 2. Có temp_ ID NHƯNG không phải từ AI (không có đầy đủ thông tin AI)
        if (!taskId) {
          return true;
        }

        if (taskId.startsWith('temp_')) {
          // Nếu task có đầy đủ thông tin (name, description, evaluationGuidelines)
          // thì coi như là task từ AI và sẽ được tự động lưu
          const hasAIContent = task.name && (task.description || task.evaluationGuidelines);
          return !hasAIContent; // Chỉ cảnh báo nếu KHÔNG có nội dung AI
        }
      }

      return false;
    });

    return unsavedTasks;
  }, [scenarios, activeTabKey]);


  // Filter roleplay instructions by simulation type
  const filteredRoleplayInstructions = useMemo(() => {

    if (!currentSimulationType) {
      return roleplayInstructions;
    }

    return roleplayInstructions.filter(
      instruction => instruction.simulationType === currentSimulationType,
    );

  }, [roleplayInstructions, currentSimulationType]);

  // Memoize unsaved tasks calculation
  const unsavedTasks = useMemo(() => {
    return checkUnsavedTasks();
  }, [checkUnsavedTasks, taskUpdateTrigger]);



  const renderTabLabel = (scenario) => {
    // Không cho phép edit tên tab trực tiếp - chỉ hiển thị tên
    return (
      <div className="tab-label">
        <span>{scenario.name}</span>
      </div>
    );
  };

  const getCurrentScenario = () => {
    return scenarios.find(s => s._id === activeTabKey);
  };

  const getCurrentTasksList = () => {
    const currentScenario = getCurrentScenario();

    // Luôn trả về taskIds nếu có, bất kể là object hay string ID
    if (currentScenario?.taskIds && Array.isArray(currentScenario.taskIds)) {
      return currentScenario.taskIds;
    }

    return [];
  };


  // Expose save method for parent component
  const saveAllScenarios = async () => {
    try {

      // Validate current form
      const formValues = await form.validateFields();

      // Check for truly unsaved tasks (không bao gồm task từ AI sẽ được tự động lưu)
      const allUnsavedTasks = [];
      scenarios.forEach((scenario, index) => {
        if (scenario.taskIds) {
          const scenarioUnsavedTasks = scenario.taskIds.filter(task => {
            if (typeof task === 'object') {
              const taskId = task._id;

              // Không coi task từ AI là "unsaved" vì chúng sẽ được tự động lưu
              if (taskId && taskId.startsWith('temp_')) {
                const hasAIContent = task.name && (task.description || task.evaluationGuidelines);
                return !hasAIContent; // Chỉ cảnh báo nếu KHÔNG có nội dung AI
              }

              return !taskId; // Chỉ cảnh báo task không có ID
            }
            return false;
          });

          if (scenarioUnsavedTasks.length > 0) {
            allUnsavedTasks.push({
              scenarioName: scenario.name || `${t('SCENARIO')} ${index + 1}`,
              tasks: scenarioUnsavedTasks,
            });
          }
        }
      });

      // Cảnh báo về các task thực sự chưa lưu (không bao gồm task từ AI)
      if (allUnsavedTasks.length > 0) {
        const warningMessage = allUnsavedTasks.map(item =>
          `${item.scenarioName}: ${item.tasks.map(task => task.name || t('UNNAMED_TASK')).join(', ')}`,
        ).join('\n');

        console.warn('Truly unsaved tasks found:', warningMessage);
        // Không hiển thị toast warning ở đây vì đã có cảnh báo trong UI
        // Không return false, tiếp tục lưu với các task đã saved
      }

      // Save all scenarios that need saving
      const savePromises = scenarios.map(async (scenario) => {
        if (scenario.isNew || scenario.isModified) {

          // Tự động lưu các task có temp ID trước
          const processedTaskIds = [];

          if (scenario.taskIds && Array.isArray(scenario.taskIds)) {
            for (const task of scenario.taskIds) {
              const taskId = typeof task === 'object' ? task._id : task;

              if (taskId && taskId.startsWith('temp_') && typeof task === 'object') {
                // Task từ AI cần được lưu

                try {
                  const taskData = {
                    ...task,
                    courseId,
                    orderInScenario: processedTaskIds.length + 1,
                  };

                  // Remove temp ID
                  delete taskData._id;
                  delete taskData.key;

                  const savedTask = await createRoleplayTask(taskData);
                  if (savedTask && savedTask._id) {
                    processedTaskIds.push(savedTask._id);
                  } else {
                    console.error('Failed to save AI task:', task);
                    toast.error(t('SAVE_TASK_ERROR', `Không thể lưu task: ${task.name || 'Unnamed'}`));
                  }
                } catch (error) {
                  console.error('Error saving AI task:', error);
                  toast.error(t('SAVE_TASK_ERROR', `Lỗi khi lưu task: ${task.name || 'Unnamed'}`));
                }
              } else if (taskId && !taskId.startsWith('temp_')) {
                // Task đã có ID thật
                processedTaskIds.push(taskId);
              }
            }
          }

          const scenarioData = {
            ...scenario,
            courseId,
            // If this is the active tab, use form values
            ...(scenario._id === activeTabKey ? formValues : {}),
            // Use processed task IDs (bao gồm cả task vừa lưu từ AI)
            taskIds: processedTaskIds,
            // Ensure aiPersonaId is set from selectedPersonaForPreview if this is the active tab
            ...(scenario._id === activeTabKey && selectedPersonaForPreview ? {
              aiPersonaId: selectedPersonaForPreview._id
            } : {}),
          };

          // Remove temporary fields
          delete scenarioData.isNew;
          delete scenarioData.isModified;

          // Ensure aiPersonaId is a string ID, not an object
          if (scenarioData.aiPersonaId && typeof scenarioData.aiPersonaId === 'object') {
            scenarioData.aiPersonaId = scenarioData.aiPersonaId._id;
          }

          // Ensure roleplayInstructionId is a string ID, not an object
          if (scenarioData.roleplayInstructionId && typeof scenarioData.roleplayInstructionId === 'object') {
            scenarioData.roleplayInstructionId = scenarioData.roleplayInstructionId._id;
          }

          let response;
          if (scenario.isNew) {
            response = await createAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
          } else {
            response = await updateAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
          }

          // Cập nhật scenario state với task IDs thật sau khi lưu thành công
          if (response && response._id) {
            setScenarios(prevScenarios =>
              prevScenarios.map(s =>
                s._id === scenario._id
                  ? {
                      ...response,
                      isNew: false,
                      isModified: false,
                      // Cập nhật taskIds với IDs thật từ database
                      taskIds: processedTaskIds
                    }
                  : s
              )
            );
          }

          return response;
        }
        return scenario;
      });

      const results = await Promise.all(savePromises);

      // Update scenarios state with saved data
      const updatedScenarios = scenarios.map((scenario, index) => {
        const result = results[index];
        if (result && result._id && result._id !== scenario._id) {
          // New scenario created - use data from server
          return {...result, isNew: false, isModified: false};
        } else if (scenario.isModified && result) {
          // Existing scenario updated - merge with server data
          return {...result, isModified: false};
        }
        return scenario;
      });

      setScenarios(updatedScenarios);

      // Update active tab key if it was a new scenario
      const currentScenario = getCurrentScenario();
      if (currentScenario && currentScenario.isNew) {
        const newScenario = updatedScenarios.find(s => s.name === currentScenario.name && !s.isNew);
        if (newScenario) {
          setActiveTabKey(newScenario._id);
        }
      }

      // Refresh form with updated data
      setTimeout(() => {
        const updatedCurrentScenario = updatedScenarios.find(s => s._id === activeTabKey);
        if (updatedCurrentScenario) {
          updateFormWithScenario(updatedCurrentScenario);
        }
      }, 100);

      // Notify parent component
      if (onScenariosChange) {
        onScenariosChange(updatedScenarios);
      }

      // Đếm số task từ AI đã được lưu
      let savedAITasksCount = 0;
      results.forEach(result => {
        if (result && result.taskIds) {
          savedAITasksCount += result.taskIds.length;
        }
      });


      // Không hiển thị toast success ở đây vì parent component sẽ hiển thị thông báo tổng thể
      return true;
    } catch (error) {
      console.error('Error saving scenarios:', error);
      toast.error(t('SAVE_SCENARIOS_ERROR'));
      return false;
    }
  };

  // Expose this method to parent
  useImperativeHandle(ref, () => ({
    saveAllScenarios,
    checkUnsavedTasks,
  }));

  const tabItems = scenarios.map(scenario => ({
    key: scenario._id,
    label: renderTabLabel(scenario),
    closable: true, // Cho phép đóng tab (hiển thị nút X)
    children: (
      <div className="scenario-tab-content">
        <AntForm
          form={form}
          layout="vertical"
          onValuesChange={(changedValues) => {
            updateCurrentScenario(changedValues);
          }}
        >
          <Card
            title={t('SCENARIO_INFORMATION') }
            className="basic-info-card"
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label={t('SCENARIO_NAME')}
                  rules={[
                    {required: true, message: t('PLEASE_ENTER_SCENARIO_NAME')},
                    {max: 100, message: t('SCENARIO_NAME_TOO_LONG')},
                  ]}
                >
                  <Input
                    placeholder={t('ENTER_SCENARIO_NAME')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="estimatedCallTimeInMinutes"
                  label={t('ESTIMATED_TIME_MINUTES')}
                  rules={[{required: true, message: t('PLEASE_ENTER_ESTIMATED_CALL_TIME')}]}
                >
                  <InputNumber
                    min={1}
                    max={120}
                    placeholder={t('ENTER_ESTIMATED_TIME')}
                    style={{width: '100%'}}
                    addonAfter={t('MINUTES')}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="passScore"
                  label={t('PASS_SCORE', 'Điểm đạt yêu cầu')}
                  rules={[
                    {required: true, message: t('PLEASE_ENTER_PASS_SCORE', 'Vui lòng nhập điểm đạt yêu cầu')},
                    {type: 'number', min: 0, max: 100, message: t('PASS_SCORE_RANGE', 'Điểm đạt yêu cầu phải từ 0 đến 100')}
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={100}
                    style={{width: '100%'}}
                    placeholder={t('ENTER_PASS_SCORE', 'Nhập điểm đạt yêu cầu (0-100)')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                {/* Placeholder for future field */}
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="simulationType"
                  label={t('SCENARIO_TYPE', 'Loại kịch bản')}
                  rules={[{required: true, message: t('PLEASE_SELECT_SCENARIO_TYPE', 'Vui lòng chọn loại kịch bản')}]}
                >
                  <Select
                    placeholder={t('SELECT_SCENARIO_TYPE', 'Chọn loại kịch bản')}
                    onChange={(value) => {
                      // Set current simulation type for filtering
                      setCurrentSimulationType(value);
                      // Reset roleplay instruction khi thay đổi simulation type
                      form.setFieldValue('roleplayInstructionId', undefined);
                      updateCurrentScenario({simulationType: value, roleplayInstructionId: undefined});
                    }}
                  >
                    <Option value="Sale">{t('SALE_SCENARIO', 'Bán hàng')}</Option>
                    <Option value="Service">{t('SERVICE_SCENARIO', 'Dịch vụ khách hàng')}</Option>
                    <Option value="HR">{t('HR_SCENARIO', 'Nhân sự')}</Option>
                    <Option value="Education">{t('EDUCATION_SCENARIO', 'Giáo dục')}</Option>
                    <Option value="Other">{t('OTHER_SCENARIO', 'Khác')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="roleplayInstructionId"
                  label={t('ROLEPLAY_INSTRUCTION')}
                  rules={[{required: true, message: t('PLEASE_SELECT_ROLEPLAY_INSTRUCTION')}]}
                >
                  <Select
                    placeholder={
                      !currentSimulationType
                        ? t('PLEASE_SELECT_SCENARIO_TYPE_FIRST', 'Vui lòng chọn loại kịch bản trước')
                        : filteredRoleplayInstructions.length === 0
                          ? t('NO_ROLEPLAY_INSTRUCTIONS_AVAILABLE')
                          : t('SELECT_ROLEPLAY_INSTRUCTION')
                    }
                    allowClear
                    showSearch
                    disabled={!currentSimulationType}
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    notFoundContent={
                      !currentSimulationType
                        ? t('PLEASE_SELECT_SCENARIO_TYPE_FIRST', 'Vui lòng chọn loại kịch bản trước')
                        : t('NO_ROLEPLAY_INSTRUCTIONS_FOUND')
                    }
                  >
                    {filteredRoleplayInstructions.map(instruction => (
                      <Option key={instruction._id} value={instruction._id}>
                        {instruction.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>

              <Col span={24}>
                <Form.Item
                  name="description"
                  label={t('SCENARIO_DESCRIPTION', 'Mô tả kịch bản')}
                >
                  <Input.TextArea
                    rows={3}
                    placeholder={t('ENTER_SCENARIO_DESCRIPTION', 'Nhập mô tả cho kịch bản này')}
                  />
                </Form.Item>
              </Col>
            </Row>

          </Card>

          {/* AI Persona Information Section */}
          <Card
            title={t('AI_PERSONA_INFO', 'Thông tin AI Persona')}
            className="ai-config-card"
            extra={
              <div style={{ display: 'flex', gap: '8px' }}>
                {selectedPersonaForPreview && (
                  <AntButton
                    size="small"
                    type={BUTTON.DEEP_NAVY}
                    icon={<EditOutlined />}
                    onClick={() => handlePersonaEdit(selectedPersonaForPreview)}
                  >
                    {t('EDIT', 'Sửa')}
                  </AntButton>
                )}
                <AntButton
                  size="small"
                  type={BUTTON.DEEP_NAVY}
                  icon={<PlusOutlined />}
                  onClick={() => setIsCreatePersonaModalOpen(true)}
                >
                  {t('ADD_NEW', 'Thêm mới')}
                </AntButton>
              </div>
            }
          >
            {selectedPersonaForPreview ? (
              <AIPersonaSetupCard
                persona={selectedPersonaForPreview}
                hideEditButton={true}
                onLearnMore={() => {
                }}
              />
            ) : (
              <div className="no-persona-message">
                <div className="no-persona-icon">
                  <RobotOutlined />
                </div>
                <div className="no-persona-text">
                  <p>{t('NO_PERSONA_SELECTED', 'Chưa có thông tin AI Persona')}</p>
                  <p className="no-persona-description">
                    {t('NO_PERSONA_DESCRIPTION', 'Vui lòng thêm mới AI Persona để bắt đầu thiết lập kịch bản')}
                  </p>
                </div>
              </div>
            )}
          </Card>

          {/* Tasks Management Section */}
          <Card
            title={
              <div className="tasks-header">
                {t('TASKS_MANAGEMENT')}
              </div>
            }
            extra={
              <Space>
                <AntButton
                  onClick={() => {
                    const newId = `temp_${Date.now()}`;
                    const newTask = {
                      _id: newId,
                      name: '',
                      description: '',
                      evaluationGuidelines: '',
                      weight: 0,
                      exampleVideoUrl: '',
                      helpfulLinks: [],
                      isMakeOrBreak: false,
                    };
                    const currentScenario = getCurrentScenario();
                    if (currentScenario) {
                      const updatedTaskIds = [...(currentScenario.taskIds || []), newTask];
                      updateCurrentScenario({taskIds: updatedTaskIds});
                      setTaskUpdateTrigger(prev => prev + 1);
                    }
                    // Gọi callback với single task (không phải bulk add)
                    if (onTaskAdd) onTaskAdd(newTask);
                  }}
                  size="small"
                  icon={<PlusOutlined />}
                  type={BUTTON.DEEP_NAVY}
                >
                  {t('ADD_TASK_TOPIC', 'Thêm chủ đề / nhiệm vụ')}
                </AntButton>
                <AntButton
                  onClick={() => {
                    // Trigger AI modal from TaskEvaluationDisplayCard
                    if (taskCardRef.current && taskCardRef.current.showAIModal) {
                      taskCardRef.current.showAIModal();
                    }
                  }}
                  icon={<RobotOutlined />}
                  disabled={!courseId}
                  type="primary"
                  style={{background: '#722ED1'}}
                  size="small"
                >
                  {t('CREATE_FROM_AI', 'Tạo từ AI')}
                </AntButton>
              </Space>
            }
            className="tasks-management-card"
          >

            <TaskEvaluationDisplayCard
              ref={taskCardRef}
              dataSource={getCurrentTasksList()}
              hideButtons={true}
              key={`tasks-${activeTabKey}-${taskUpdateTrigger}`} // Force re-render when tasks change
              onTaskAdd={(taskData) => {
                const currentScenario = getCurrentScenario();

                if (currentScenario) {
                  const currentTaskIds = currentScenario.taskIds || [];

                  let updatedTaskIds;

                  // Xử lý bulk add từ AI
                  if (taskData && taskData.type === 'BULK_ADD' && taskData.tasks) {
                    updatedTaskIds = [...currentTaskIds, ...taskData.tasks];
                  } else {
                    // Xử lý add single task
                    updatedTaskIds = [...currentTaskIds, taskData];
                  }

                  // Sử dụng updateCurrentScenario để thống nhất
                  updateCurrentScenario({taskIds: updatedTaskIds});

                  setTaskUpdateTrigger(prev => prev + 1);
                }

                // Gọi callback parent nếu có
                if (onTaskAdd) {
                  if (taskData && taskData.type === 'BULK_ADD' && taskData.tasks) {
                    // Gọi callback cho từng task trong bulk add
                    taskData.tasks.forEach(task => onTaskAdd(task));
                  } else {
                    onTaskAdd(taskData);
                  }
                }
              }}
              onTaskUpdate={(updatedTask) => {
                const currentScenario = getCurrentScenario();
                if (currentScenario) {
                  // Cập nhật task trong scenarios state
                  setScenarios(prevScenarios => {
                    return prevScenarios.map(scenario => {
                      if (scenario._id === activeTabKey) {
                        const updatedTaskIds = (scenario.taskIds || []).map(task => {
                          // Nếu task là object và có _id trùng với updatedTask
                          if (typeof task === 'object' && task._id === updatedTask._id) {
                            return updatedTask;
                          }
                          // Nếu task là object và có _id trùng với original_temp_id (task mới được tạo)
                          if (typeof task === 'object' && updatedTask.original_temp_id && task._id === updatedTask.original_temp_id) {
                            const {original_temp_id, ...taskFromServer} = updatedTask;
                            return taskFromServer;
                          }
                          // Nếu task là string ID và trùng với updatedTask._id
                          if (typeof task === 'string' && task === updatedTask._id) {
                            return updatedTask;
                          }
                          // Nếu task là string ID và trùng với original_temp_id
                          if (typeof task === 'string' && updatedTask.original_temp_id && task === updatedTask.original_temp_id) {
                            return updatedTask;
                          }
                          return task;
                        });

                        return {...scenario, taskIds: updatedTaskIds};
                      }
                      return scenario;
                    });
                  });

                  // Force re-render
                  setTaskUpdateTrigger(prev => prev + 1);
                }

                // Gọi callback parent nếu có
                if (onTaskUpdate) {
                  onTaskUpdate(updatedTask);
                }
              }}
              onTaskDelete={(taskId) => {
                const currentScenario = getCurrentScenario();
                if (currentScenario) {
                  const updatedTaskIds = (currentScenario.taskIds || []).filter(task => {
                    const currentTaskId = typeof task === 'object' ? task._id : task;
                    return currentTaskId !== taskId;
                  });
                  updateCurrentScenario({taskIds: updatedTaskIds});
                  setTaskUpdateTrigger(prev => prev + 1); // Force re-render
                }
                if (onTaskDelete) onTaskDelete(taskId);
              }}
              courseId={courseId}
            />
          </Card>

          {/* Unsaved tasks warning */}
          {unsavedTasks.length > 0 && (
            <Alert
              message={t('UNSAVED_TASKS_WARNING', {
                tasks: unsavedTasks.map(task => task.name || t('UNNAMED_TASK')).join(', '),
              })}
              description={t('PLEASE_SAVE_TASKS_BEFORE_SAVING_COURSE')}
              type="warning"
              showIcon
            />
          )}
        </AntForm>
      </div>
    ),
  }));

  return (
    <div className="ai-scenario-tabs">
      <Tabs
        type="editable-card"
        activeKey={activeTabKey}
        onChange={setActiveTabKey}
        onEdit={handleTabEdit}
        items={tabItems}
        addIcon={
          <Tooltip title={t('ADD_NEW_SCENARIO', 'Thêm kịch bản mới')}>
            <PlusOutlined />
          </Tooltip>
        }
        hideAdd={false}
      />

      {/* Modal tạo AI Persona */}
      <CreateAIPersonaModal
        isOpen={isCreatePersonaModalOpen}
        onClose={() => setIsCreatePersonaModalOpen(false)}
        onSuccess={handlePersonaCreated}
        courseId={courseId}
      />

      {/* Modal edit AI Persona */}
      <EditAIPersonaModal
        isOpen={isEditPersonaModalOpen}
        onClose={() => setIsEditPersonaModalOpen(false)}
        onSuccess={handlePersonaUpdated}
        persona={editingPersona}
      />
    </div>
  );
});

export default AIScenarioTabs;
