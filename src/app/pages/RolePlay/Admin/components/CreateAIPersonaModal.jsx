import React, {useState} from 'react';
import {Modal, Button, Form, Input, InputNumber, Select, Tooltip, Row, Col} from 'antd';
import {InfoCircleOutlined, RobotOutlined} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';

import {createAIPersona, createAIPersonaFromCourseContext} from '@services/RolePlay/AIPersonaService';
import {uploadFile} from '@services/File';
import {toast} from '@component/ToastProvider';
import UploadImagePreview from '@component/UploadImagePreview';
import {AntForm} from '@component/AntForm';
import AntButton from "@component/AntButton";
import { BUTTON, PAGINATION_INIT } from "@constant"; // Bỏ SIMULATION_TYPE_OPTIONS nếu không dùng

const CreateAIPersonaModal = ({isOpen, onClose, onSuccess, courseId}) => {
  const {t} = useTranslation();
  const [personaForm] = Form.useForm();
  const [isCreatingAIPersona, setIsCreatingAIPersona] = useState(false);
  const [avatarId, setAvatarId] = useState(null);
  const [isLoadingAvatar, setIsLoadingAvatar] = useState(false);

  // State cho modal tạo bằng AI
  const [isAIModalVisible, setIsAIModalVisible] = useState(false);
  const [userPrompt, setUserPrompt] = useState('');
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false);

  const handleUploadAvatar = async file => {
    setIsLoadingAvatar(true);
    try {
      const uploadResponse = await uploadFile(file, {folder: 'image'});
      if (uploadResponse && uploadResponse._id) {
        setAvatarId(uploadResponse._id);
        personaForm.setFieldsValue({avatarId: uploadResponse._id});
        toast.success(t('UPLOAD_AVATAR_SUCCESS'));
      } else {
        toast.error(t('UPLOAD_AVATAR_ERROR'));
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      toast.error(t('UPLOAD_AVATAR_ERROR'));
    } finally {
      setIsLoadingAvatar(false);
    }
  };

  const handleClearAvatar = () => {
    setAvatarId(null);
    personaForm.setFieldsValue({avatarId: null});
  };

  const handleCreateAIPersona = async () => {
    try {
      const values = await personaForm.validateFields();
      setIsCreatingAIPersona(true);

      const payloadToSend = {
        ...values,
      };

      const apiResponse = await createAIPersona(payloadToSend);

      if (apiResponse) {
        toast.success(t('CREATE_AI_PERSONA_SUCCESS'));
        handleCancel();

        // Gọi callback onSuccess với AI Persona vừa tạo
        if (onSuccess && typeof onSuccess === 'function') {
          onSuccess(apiResponse);
        }
      } else {
        toast.error(t('CREATE_AI_PERSONA_ERROR'));
      }
    } catch (error) {
      console.error('Error creating AI Persona:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsCreatingAIPersona(false);
    }
  };

  const handleCancel = () => {
    onClose();
    personaForm.resetFields();
    setAvatarId(null);
    setUserPrompt('');
  };

  const showAIModal = () => {
    setIsAIModalVisible(true);
  };

  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setUserPrompt('');
  };

  const handleGenerateWithAI = async () => {
    if (!courseId) {
      toast.error(t('COURSE_ID_REQUIRED'));
      return;
    }

    if (!userPrompt || userPrompt.trim() === '') {
      toast.error(t('PROMPT_REQUIRED'));
      return;
    }

    setIsGeneratingWithAI(true);
    try {
      const generatedPersona = await createAIPersonaFromCourseContext(courseId, userPrompt);
      if (generatedPersona) {
        // Điền thông tin vào form
        personaForm.setFieldsValue({
          name: generatedPersona.name,
          age: generatedPersona.age,
          gender: generatedPersona.gender,
          role: generatedPersona.role,
          mood: generatedPersona.mood,
          organization: generatedPersona.organization,
          personaBackground: generatedPersona.personaBackground,
          personaConcern: generatedPersona.personaConcern,
          smallTalkLikely: generatedPersona.smallTalkLikely,
          voice: generatedPersona.voice,
        });

        // Nếu có avatarId, cập nhật
        if (generatedPersona.avatarId) {
          setAvatarId(generatedPersona.avatarId);
          personaForm.setFieldsValue({avatarId: generatedPersona.avatarId});
        }

        toast.success(t('GENERATE_AI_PERSONA_SUCCESS'));
        setIsAIModalVisible(false);
        setUserPrompt('');
      } else {
        toast.error(t('GENERATE_AI_PERSONA_ERROR'));
      }
    } catch (error) {
      console.error('Error generating AI Persona:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsGeneratingWithAI(false);
    }
  };

  return (
    <>
      <Modal
        title={t('CREATE_AI_PERSONA')}
        open={isOpen}
        onCancel={handleCancel}
        width={800}
        footer={[
          <AntButton key="cancel" size={'large'} type={BUTTON.TEXT} onClick={handleCancel} disabled={isCreatingAIPersona}>
            {t('CANCEL')}
          </AntButton>,
          <AntButton
            key="ai"
            onClick={showAIModal}
            icon={<RobotOutlined />}
            disabled={isCreatingAIPersona || isLoadingAvatar || !courseId}
            type={'primary'}
            style={{background: '#722ED1', marginRight: 8}}
            size={'large'}
          >
            {t('CREATE_WITH_AI')}
          </AntButton>,
          <AntButton
            key="submit"
            type={BUTTON.DEEP_NAVY}
            onClick={handleCreateAIPersona}
            loading={isCreatingAIPersona}
            disabled={isCreatingAIPersona || isLoadingAvatar}
            size={'large'}
          >
            {t('CREATE')}
          </AntButton>,
        ]}
      >
        <AntForm form={personaForm} layout="vertical">
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="name"
                label={t('AI_PERSONA_NAME')}
                rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_NAME')})}]}
              >
                <Input placeholder={t('ENTER_AI_PERSONA_NAME')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item name="age" label={t('AGE')}>
                <Input placeholder={t('ENTER_AI_PERSONA_AGE')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item name="gender" label={t('GENDER')}>
                <Select placeholder={t('SELECT_GENDER')}>
                  <Select.Option value="male">{t('MALE')}</Select.Option>
                  <Select.Option value="female">{t('FEMALE')}</Select.Option>
                  <Select.Option value="other">{t('OTHER')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="role"
                label={t('AI_PERSONA_ROLE')}
                rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_ROLE')})}]}
              >
                <Input placeholder={t('ENTER_AI_PERSONA_ROLE')} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item name="mood" label={t('AI_PERSONA_MOOD')}>
                <Input placeholder={t('ENTER_AI_PERSONA_MOOD')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item name="organization" label={t('AI_PERSONA_ORGANIZATION')}>
                <Input placeholder={t('ENTER_AI_PERSONA_ORGANIZATION')} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label={t('AI_PERSONA_AVATAR')}>
            <UploadImagePreview
              imageId={avatarId}
              onDrop={handleUploadAvatar}
              onClear={handleClearAvatar}
              loading={isLoadingAvatar}
            />
          </Form.Item>
          <Form.Item name="avatarId" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="personaBackground"
            label={t('AI_PERSONA_BACKGROUND')}
            rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_BACKGROUND')})}]}
          >
            <Input.TextArea rows={4} placeholder={t('ENTER_AI_PERSONA_BACKGROUND')} />
          </Form.Item>

          <Form.Item name="personaConcern" label={t('AI_PERSONA_CONCERNS')}>
            <Input.TextArea rows={3} placeholder={t('ENTER_AI_PERSONA_CONCERNS')} />
          </Form.Item>

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="smallTalkLikely"
                label={
                  <span>
                    {t('AI_PERSONA_SMALL_TALK_LIKELY')}
                    <Tooltip title={t('AI_PERSONA_SMALL_TALK_LIKELY_TOOLTIP')}>
                      <InfoCircleOutlined style={{marginLeft: 4, color: 'rgba(0,0,0,.45)'}} />
                    </Tooltip>
                  </span>
                }
                rules={[{type: 'number', min: 0, max: 100, message: t('VALUE_BETWEEN_0_100')}]}
              >
                <InputNumber min={0} max={100} placeholder="0-100" style={{width: '100%'}} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="voice"
                label={t('AI_PERSONA_VOICE')}
              >
                <Input placeholder={t('ENTER_AI_PERSONA_VOICE')} />
              </Form.Item>
            </Col>
          </Row>
        </AntForm>
      </Modal>

      {/* Modal nhập prompt cho AI */}
      <Modal
        title={t('CREATE_AI_PERSONA_WITH_AI')}
        open={isAIModalVisible}
        onCancel={handleAIModalCancel}
        footer={[
          <AntButton key="cancel" size={'large'} type={BUTTON.TEXT} onClick={handleAIModalCancel} disabled={isGeneratingWithAI}>
            {t('CANCEL')}
          </AntButton>,
          <AntButton
            key="generate"
            type={BUTTON.DEEP_NAVY}
            onClick={handleGenerateWithAI}
            loading={isGeneratingWithAI}
            disabled={isGeneratingWithAI || !userPrompt}
            style={{background: '#722ED1'}}
            size={'large'}
          >
            {t('GENERATE')}
          </AntButton>,
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('AI_PROMPT')}
            required
            tooltip={t('AI_PROMPT_TOOLTIP', 'Describe the AI Persona you want to create')}
          >
            <Input.TextArea
              rows={4}
              value={userPrompt}
              onChange={e => setUserPrompt(e.target.value)}
              placeholder={t(
                'ENTER_AI_PROMPT_PLACEHOLDER',
                'E.g., A humorous persona for a bank customer service representative',
              )}
              disabled={isGeneratingWithAI}
            />
          </Form.Item>
          <div style={{marginBottom: 16}}>
            <p>
              {t('AI_PROMPT_HELP_TEXT', 'The AI will generate a persona based on your prompt and the course context.')}
            </p>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default CreateAIPersonaModal;
