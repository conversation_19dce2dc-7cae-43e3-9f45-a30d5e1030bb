import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Input, Space, Typography, Alert} from 'antd';
import {LinkOutlined, DownloadOutlined, ExclamationCircleOutlined} from '@ant-design/icons';

import AntModal from '@component/AntModal';
import AntButton from '@component/AntButton';
import TableAdmin from '@component/TableAdmin';
import {BUTTON, PAGINATION_CONFIG} from '@constant';
import {toast} from '@component/ToastProvider';

import {
  importFromGoogleSheets,
  validateGoogleSheetsUrl,
  convertSheetsDataToCourseFormat,
} from '@services/GoogleSheets/GoogleSheetsService';

import './GoogleSheetsImportModal.scss';

const {Text, Title} = Typography;

const GoogleSheetsImportModal = ({
                                   isOpen,
                                   onClose,
                                   onDataSelected,
                                 }) => {
  const {t} = useTranslation();

  const [sheetUrl, setSheetUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sheetsData, setSheetsData] = useState([]);
  const [selectedRowKey, setSelectedRowKey] = useState(null);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1); // 1: URL input, 2: Data selection

  const handleUrlChange = (e) => {
    const url = e.target.value;
    setSheetUrl(url);
    setError('');
  };

  const handleFetchData = async () => {
    if (!sheetUrl.trim()) {
      setError(t('PLEASE_ENTER_GOOGLE_SHEETS_URL', 'Vui lòng nhập URL Google Sheets'));
      return;
    }

    if (!validateGoogleSheetsUrl(sheetUrl)) {
      setError(t('INVALID_GOOGLE_SHEETS_URL', 'URL Google Sheets không hợp lệ. Vui lòng kiểm tra lại.'));
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await importFromGoogleSheets(sheetUrl);

      if (response && response.success && response.data) {
        const formattedData = convertSheetsDataToCourseFormat(response.data);

        if (formattedData.length === 0) {
          setError(t('NO_VALID_DATA_FOUND', 'Không tìm thấy dữ liệu hợp lệ. Vui lòng kiểm tra định dạng dữ liệu.'));
          return;
        }

        setSheetsData(formattedData);
        setStep(2);
        toast.success(t('FETCH_DATA_SUCCESS', `Lấy dữ liệu thành công! Tìm thấy ${formattedData.length} khóa học.`));
      } else {
        setError(t('NO_DATA_FOUND', 'Không tìm thấy dữ liệu trong Google Sheets'));
      }
    } catch (err) {
      console.error('Error fetching Google Sheets data:', err);
      const errorMessage = err.message ||
        t('FETCH_DATA_ERROR', 'Không thể lấy dữ liệu từ Google Sheets. Vui lòng kiểm tra quyền truy cập.');
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRowSelect = (selectedRowKeys, selectedRows) => {
    if (selectedRows.length > 0) {
      setSelectedRowKey(selectedRowKeys[0]);
    } else {
      setSelectedRowKey(null);
    }
  };

  const handleSelectData = () => {
    if (!selectedRowKey) {
      toast.warning(t('PLEASE_SELECT_COURSE', 'Vui lòng chọn một khóa học'));
      return;
    }

    const selectedCourse = sheetsData.find(item => item._tempId === selectedRowKey);
    if (selectedCourse) {
      onDataSelected(selectedCourse);
      handleClose();
    }
  };

  const handleClose = () => {
    setSheetUrl('');
    setSheetsData([]);
    setSelectedRowKey(null);
    setError('');
    setStep(1);
    onClose();
  };


  const handleBackToUrl = () => {
    setStep(1);
    setSheetsData([]);
    setSelectedRowKey(null);
    setError('');
  };


  const columns = [
    {
      title: t('COURSE_NAME'),
      dataIndex: 'name',
      key: 'name',
      width: 250,
      ellipsis: true,
    },
    {
      title: t('COURSE_CODE'),
      dataIndex: 'code',
      key: 'code',
      width: 300,
      ellipsis: true,
    },
    {
      title: t('VIDEO'),
      dataIndex: 'video',
      key: 'video',
      width: 150,
      ellipsis: true,
    },
    {
      title: t('LINK_DOCUMENT'),
      dataIndex: 'linkDocument',
      key: 'linkDocument',
      width: 300,
      ellipsis: true,
      render: (text) => {
        if (!text) return '-';

        // Nếu là URL, hiển thị dưới dạng link
        if (text.startsWith('http://') || text.startsWith('https://')) {
          const displayText = text.length > 40 ? `${text.substring(0, 40)}...` : text;
          return (
            <a
              href={text}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: 'var(--primary-colours-blue-navy)',
                textDecoration: 'none',
                display: 'block',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '280px'
              }}
              title={text}
            >
              {displayText}
            </a>
          );
        }

        // Nếu không phải URL, hiển thị text thường
        const displayText = text.length > 40 ? `${text.substring(0, 40)}...` : text;
        return (
          <span
            style={{
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '280px'
            }}
            title={text}
          >
            {displayText}
          </span>
        );
      },
    },
  ];

  const rowSelection = {
    type: 'radio',
    selectedRowKeys: selectedRowKey ? [selectedRowKey] : [],
    onChange: handleRowSelect,
  };

  return (
    <AntModal
      title={t('IMPORT_FROM_GOOGLE_SHEETS')}
      open={isOpen}
      onCancel={handleClose}
      width={step === 1 ? 1000 : 1200}
      footerless
      className="google-sheets-import-modal"
    >
      <div className="google-sheets-import__content">
        {step === 1 && (
          <div className="url-input-step">
            <div className="step-header">
              <Title level={4} style={{ marginBottom: 8 }}>
                {t('GOOGLE_SHEETS_URL')}
              </Title>
              <Text type="secondary">
                {t('GOOGLE_SHEETS_URL_DESCRIPTION', 'Nhập URL của Google Sheets chứa dữ liệu khóa học. Đảm bảo sheet được chia sẻ công khai.')}
              </Text>
            </div>

            <div className="step-content">
              <div className="url-input-container">
                <Input
                  size="large"
                  placeholder="https://docs.google.com/spreadsheets/d/..."
                  prefix={<LinkOutlined />}
                  value={sheetUrl}
                  onChange={handleUrlChange}
                  status={error ? 'error' : ''}
                />
              </div>

              {error && (
                <Alert
                  message={error}
                  type="error"
                  icon={<ExclamationCircleOutlined />}
                  showIcon
                  className="error-alert"
                />
              )}
            </div>

            <div className="modal-footer">
              <Space>
                <AntButton
                  size="large"
                  type={BUTTON.WHITE}
                  onClick={handleClose}
                >
                  {t('CANCEL')}
                </AntButton>
                <AntButton
                  size="large"
                  type={BUTTON.DEEP_NAVY}
                  onClick={handleFetchData}
                  loading={isLoading}
                  icon={<DownloadOutlined />}
                >
                  {t('FETCH_DATA')}
                </AntButton>
              </Space>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="data-selection-step">
            <div className="step-header">
              <Title level={4} style={{ marginBottom: 8 }}>
                {t('SELECT_COURSE_DATA')}
              </Title>
              <Text type="secondary">
                {t('SELECT_COURSE_DESCRIPTION', 'Chọn một khóa học từ danh sách dưới đây để nhập vào form.')}
              </Text>
            </div>

            <div className="data-table-container">
              <TableAdmin
                columns={columns}
                dataSource={sheetsData}
                rowSelection={rowSelection}
                rowKey="_tempId"
                pagination={{
                  ...PAGINATION_CONFIG,
                  pageSizeOptions: [5, 10, 15],
                  defaultPageSize: 5,
                  showSizeChanger: true,
                }}
                size="middle"
                bordered
                scroll={{ x: 1000 }}
              />
            </div>

            <div className="modal-footer">
              <Space>
                <AntButton
                  size="large"
                  type={BUTTON.WHITE}
                  onClick={handleBackToUrl}
                >
                  {t('BACK')}
                </AntButton>
                <AntButton
                  size="large"
                  type={BUTTON.WHITE}
                  onClick={handleClose}
                >
                  {t('CANCEL')}
                </AntButton>
                <AntButton
                  size="large"
                  type={BUTTON.DEEP_NAVY}
                  onClick={handleSelectData}
                  disabled={!selectedRowKey}
                >
                  {t('SELECT_DATA')}
                </AntButton>
              </Space>
            </div>
          </div>
        )}
      </div>
    </AntModal>
  );
};

export default GoogleSheetsImportModal;
