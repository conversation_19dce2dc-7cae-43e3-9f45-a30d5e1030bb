.google-sheets-import-modal {
  .ant-modal-content.modal-content {
    box-shadow: 0px 4px 30px 0px #00000026;
  }

  .google-sheets-import__content {
    //padding: 24px;
  }

  .step-header {
    margin-bottom: 16px;

    .ant-typography {
      margin-bottom: 4px;
    }
  }

  .url-input-step {
    .step-content {
      margin-bottom: 20px;

      .url-input-container {
        margin-bottom: 16px;

        .ant-input-affix-wrapper {
          border-radius: 4px;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--primary-colours-blue-navy);
          }

          &:focus-within {
            border-color: var(--primary-colours-blue-navy);
            box-shadow: 0 0 0 2px rgba(9, 25, 107, 0.1);
          }

          &.ant-input-affix-wrapper-status-error {
            border-color: var(--red);

            &:focus-within {
              box-shadow: 0 0 0 2px rgba(255, 78, 131, 0.1);
            }
          }

          .ant-input-prefix {
            color: var(--typo-colours-support-blue-light);
            margin-right: 12px;
          }
        }
      }

      .error-alert {
        margin: 16px 0;
        border-radius: 4px;
        border: 1px solid var(--red-light-2);
        background-color: var(--red-light-1);

        .ant-alert-message {
          color: var(--red);
          font-weight: 500;
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
    }
  }

  .data-selection-step {
    .data-table-container {
      margin: 16px 0;

      // Sử dụng styling từ TableAdmin component
      .admin-table {
        .ant-table {
          border-radius: 8px;

          .ant-table-thead > tr > th {
            background-color: var(--background-light-background-grey);
            border-bottom: 1px solid var(--background-light-background-grey);
            font-weight: 600;
            color: var(--primary-colours-blue-navy);
            padding: 12px 16px;
          }

          .ant-table-tbody > tr {
            transition: all 0.2s ease;

            > td {
              padding: 12px 16px;
              vertical-align: top;

              // Styling cho link document column
              a {
                color: var(--primary-colours-blue-navy);
                text-decoration: none;
                transition: all 0.2s ease;

                &:hover {
                  color: var(--primary-colours-blue-light);
                  text-decoration: underline;
                }
              }
            }

            &:hover {
              background-color: var(--navy-light-1);
            }

            &.ant-table-row-selected {
              background-color: var(--navy-light-1);

              &:hover {
                background-color: var(--navy-light-2);
              }
            }
          }
        }
      }

      .ant-radio-wrapper {
        .ant-radio {
          .ant-radio-inner {
            border-color: var(--primary-colours-blue-navy);

            &:after {
              background-color: var(--primary-colours-blue-navy);
            }
          }

          &.ant-radio-checked .ant-radio-inner {
            border-color: var(--primary-colours-blue-navy);
            background-color: var(--primary-colours-blue-navy);
          }
        }
      }
    }

    // Pagination styling sẽ được kế thừa từ TableAdmin component
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .google-sheets-import__content {
    padding: 16px;
  }

  .data-table-container {
    .ant-table {
      font-size: 13px;

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 6px;
      }
    }
  }

  .modal-footer {
    .ant-space {
      width: 100%;
      justify-content: space-between;

      .ant-btn {
        flex: 1;
        margin: 0 4px;
      }
    }
  }
}
