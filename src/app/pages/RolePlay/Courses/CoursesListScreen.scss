.courses-list-screen {
  margin-top: 16px;
  padding: 32px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
  box-shadow: 0 4px 20px 0 #0000001a;
  display: flex;
  justify-content: center;
  flex-direction: column; // Changed to column for potential title later
  align-items: center;

  &__container {
    min-height: 100vh;
    // max-width: 928px; // Consistent with TotalTopicScreen
    width: 100%; // Ensure it takes full width up to max-width
    flex: 1;
  }

  // Optional: Add a title style if needed, similar to TotalTopicScreen
  &__main-title {
    font-family: Inter, sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #09196b;
    margin-bottom: 24px;
    text-align: left;
  }

  &__header {
    margin-bottom: 24px;
    width: 100%;

    .courses-list-screen__title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      line-height: 28px;
      background: linear-gradient(to right, #0c4da2, #9c3de4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &__search-container {
    display: flex;
    gap: 16px;
  }

  .ant-input-affix-wrapper {
    border-radius: 16px;
    border: 1px solid #e2e8ff;
    background-color: #f3f5f9;
    padding: 12px 16px;
    width: 300px;

    @media screen and (max-width: 1024px) {
      width: 298px;
    }

    @media screen and (max-width: 768px) {
      width: 100%; // Make search input full width on smaller screens
    }

    .ant-input {
      font-family: Inter, sans-serif;
      font-size: 16px;
      font-weight: 500;
      color: #09196b;

      &::placeholder {
        color: #8e9bad;
      }
    }
  }

  &__search-icon {
    width: 16px;
    height: 16px;
    // color: #09196b; // If SVG needs color
  }

  &__type-select {
    width: 220px;
    height: 48px;

    @media screen and (max-width: 768px) {
      width: 100%; // Make select full width on smaller screens
      margin-top: 8px; // Add some space if it stacks below search
    }

    .ant-select-selector {
      padding: 12px 16px !important; // Antd specificity
      border-radius: 16px !important;
      border: 1px solid #e2e8ff !important;
      background-color: #f3f5f9 !important;
      color: #09196b !important;
      height: 48px !important;
      display: flex;
      align-items: center;

      .ant-select-selection-placeholder {
        color: #8e9bad;
      }

      .ant-select-selection-item,
      .ant-select-selection-placeholder {
        font-family: Inter, sans-serif;
        font-size: 16px;
        font-weight: 500;
        // color: #09196b;
        line-height: normal; // Adjusted from 20px to normal
      }
    }

    .ant-select-arrow {
      .anticon-down {
        display: none; // Hide default arrow
      }

      &::after {
        content: '';
        display: block;
        width: 24px;
        height: 24px;
        background-image: url('@src/assets/icons/selector-arrow-courses.svg'); // Ensure this path is correct
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        transition: transform 0.3s;
      }
    }

    &.ant-select-open .ant-select-arrow::after {
      transform: rotate(180deg);
    }
  }

  &__sort-button {
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    background: none;
    border-radius: 16px;
    padding: 12px 16px;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: #0c4da2;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
    }
  }

  &__sort-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
    filter: brightness(0) saturate(100%) invert(28%) sepia(97%) saturate(471%) hue-rotate(186deg) brightness(93%) contrast(101%);

    &--desc {
      transform: rotate(180deg);
    }
  }

  &__courses-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // Adjusted minmax for the new card size
    gap: 24px; // Increased gap for better spacing
    width: 100%;
  }

  &__course-card {
    border-radius: 16px; // Rounded corners for the card
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08); // Softer shadow
    overflow: hidden;
    background-color: #fff; // Ensure card background is white
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.12);
    }

    .ant-card-cover {
      img.course-image {
        object-fit: cover;
        width: 100%;
        height: 160px; // Fixed height for the image
        // aspect-ratio: 16 / 9; // Or use aspect ratio
      }
    }

    .ant-card-body {
      padding: 16px;
      // Removed fixed height/aspect-ratio to allow content to define height
    }

    .course-card__content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .time-progress-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .course-title {
      font-family: Inter, sans-serif;
      font-size: 16px; // Slightly larger title
      font-weight: 600;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      min-height: calc(2 * 1.4em); // Min height for 2 lines, adjust 1.4em based on line-height
      line-height: 1.4em;
    }

    .course-time,
    .course-progress {
      margin: 0;
      font-family: Inter, sans-serif;
      font-size: 13px; // Smaller text for meta info
      line-height: 18px;
      font-weight: 400;
      display: flex;
      align-items: center;
      padding: 2px 8px;
      border-radius: 16px;
    }

    .course-time {
      background-color: #ddfff2;
      color: #17b177;
    }

    .course-progress {
      background-color: #e4dffe;
      color: #4d45be;
    }

    .course-description {
      font-family: Inter, sans-serif;
      font-size: 14px;
      color: #262626; // Darker gray for description
      margin: 0;
      line-height: 18px;
      max-width: 212px;
      width: 100%;
    }

    // Removed action button from within the card as the whole card is clickable
    // If a specific button is needed, it can be added back here.
  }

  &__loading,
  &__empty {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px; // Give some space for loading/empty states
    width: 100%;
  }

  &__load-more {
    margin-top: 32px;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  &__load-more-button.ant-btn {
    min-width: 180px;
    height: 48px;
    padding: 12px 24px;
    border-radius: 16px;
    background: #09196b;
    color: white;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;

    &:hover,
    &:focus {
      background: #0c2285 !important;
      color: white !important;
    }

    &:disabled {
      background-color: #f5f5f5;
      color: #bfbfbf;
      cursor: not-allowed;
    }
  }
}

// Dropdown styles for Select component (similar to TotalTopicScreen)
.ant-select-dropdown {
  .ant-select-item {
    .ant-select-item-option-content {
      font-family: Inter, sans-serif;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #09196b;
    }

    &.ant-select-item-option-active {
      background-color: #e7e5ff !important; // Antd specificity

      .ant-select-item-option-content {
        color: #09196b !important;
      }
    }

    &.ant-select-item-option-selected {
      background-color: #09196b !important; // Using var(--primary-colours-blue-navy) if defined, else direct hex

      .ant-select-item-option-content {
        color: #ffffff !important;
      }
    }
  }
}

// Responsive adjustments for header items
@media screen and (max-width: 768px) {
  .courses-list-screen__header {
    flex-direction: column;
    align-items: stretch; // Make items take full width
    gap: 16px; // Add gap between stacked items
  }

  .courses-list-screen__search-container {
    flex-direction: column;
    width: 100%;
  }

  .courses-list-screen__search-input,
  .courses-list-screen__type-select {
    width: 100% !important; // Override inline styles or more specific selectors if necessary
  }

  .courses-list-screen__sort-button {
    align-self: flex-end; // Align sort button to the right if header stacks
  }
}