import React, { useState } from 'react';
import { Progress } from 'antd';
import { PauseOutlined, PlayCircleOutlined, CloseOutlined, AudioMutedOutlined, AudioOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import DefaultPersonaImageUrl from '@src/asset/image/project-default.svg';
import AZURE_MIC from "@src/asset/icon/azure-mic-open.svg";
import AZURE_STOP from "@src/asset/icon/azure-mic-close.svg";
import CustomLiveAudioVisualizer from './CustomLiveAudioVisualizer';
import { useSelector } from 'react-redux';
import AntButton from "@component/AntButton";
import { BUTTON } from '@constant';
import { API } from '@src/constants/api';
import SCB_LOGO from "@src/asset/logo/SCB.svg";
import IconEndCall from '@src/app/component/SvgIcons/EndCall/index.js';

const NewSessionMainScreen = ({
  course,
  scenariosDetail,
  persona,
  videoRef,
  transcript,
  elapsedTime,
  totalDurationSeconds,
  progressPercent,
  paused,
  sessionEnded,
  socketStatus,
  sessionStarted,
  isAiSpeaking,
  currentAudioFrameForVisualizer,
  isAiProcessing,
  isAiInterrupted,
  aiAudioFrameForVisualizer,
  onTogglePause,
  onEndSession,
  onBackToCourseList,
  SOCKET_STATUS,
  mediaError,
  onToggleMic,
  micEnabled,
}) => {
  const { t } = useTranslation();
  const { user } = useSelector(state => state.auth);
  const avatarUrl = persona?.avatarId ? API.STREAM_ID.format(persona.avatarId) : DefaultPersonaImageUrl;

  // Format time to MM:SS
  const formatTime = seconds => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className='w-[100%] flex justify-center items-start gap-6'>
      <div className='flex flex-col gap-6 w-[100%] items-center justify-center'>
        <div className="role-play-session-screen__chat-with-AI py-6 px-6 gap-6">
          <div className="role-play-session-screen__header">
            <div className="role-play-session-screen__course-info">
              <h2 className="role-play-session-screen__course-title">
                {scenariosDetail?.name}
              </h2>
              <div className=''>
                <span className="role-play-session-screen__date-time">{t("TOTAL_DATE_TIME", "Tổng thời gian:")} <strong>{scenariosDetail?.estimatedCallTimeInMinutes} {t("MINUTES_STB", "phút")}</strong></span>
              </div>
            </div>
            {/* <AntButton
              type={BUTTON.DEEP_NAVY2}
              // icon={<CloseOutlined />}
              onClick={onEndSession}
              disabled={sessionEnded || socketStatus !== SOCKET_STATUS.CONNECTED || !sessionStarted}
              className="role-play-session-screen__exit-button"
            >
              {t("END_SESSION_", "Kết thúc")}
            </AntButton> */}
          </div>
          <div className='w-[100%] flex flex-col justify-center items-center'>
            <div className="role-play-session-screen__video-area max-w-[760px] h-[268px] gap-6">
              {/* AI Persona Display */}
              <div className="role-play-session-screen__participant-container">
                <div className="role-play-session-screen__participant-video h-[204px]">
                  <img
                    src={avatarUrl}
                    alt={persona?.name || 'AI Persona'}
                    className="role-play-session-screen__participant-image"
                    onError={e => {
                      e.target.onerror = null;
                      e.target.src = DefaultPersonaImageUrl;
                    }}
                  />
                </div>
                <div className="role-play-session-screen__visualizer-wrapper">
                  <div className='flex gap-2 items-center'>
                    <div className='w-10 h-10 rounded-full'>
                      <img
                        src={avatarUrl}
                        alt=''
                        className="w-full h-full rounded-full object-cover"
                        onError={e => {
                          e.target.onerror = null;
                          e.target.src = DefaultPersonaImageUrl;
                        }}
                      />
                    </div>
                    <div className='flex flex-col gap-1'>
                      <div className="role-play-session-screen__participant-name">{persona?.name || 'AI Persona'}</div>
                      <div className='role-play-session-screen__participant-role'>{persona?.role}</div>
                    </div>

                  </div>
                  <CustomLiveAudioVisualizer
                    audioData={aiAudioFrameForVisualizer}
                    width={60}
                    height={40}
                    barWidth={4}
                    gap={2}
                    barColor="#52c41a"
                  />
                </div>
              </div>

              {/* User Video Display */}
              <div className="role-play-session-screen__participant-container">
                <div className="role-play-session-screen__participant-video h-[204px]">
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    playsInline
                    className="role-play-session-screen__video-element"
                    onError={e => console.error('Video element error:', e)}
                  />
                </div>
                <div className="role-play-session-screen__visualizer-wrapper">
                  <div className='flex gap-2 items-center'>
                    <div className='w-10 h-10 rounded-full'>
                      <img
                        src={SCB_LOGO}
                        alt=''
                        className="w-full h-full rounded-full object-cover"
                        onError={e => {
                          e.target.onerror = null;
                          e.target.src = DefaultPersonaImageUrl;
                        }}
                      />
                    </div>
                    <div className='flex flex-col gap-1'>
                      <div className="role-play-session-screen__participant-name">{user?.fullName || t('YOU', 'Bạn')}</div>
                      <div className='role-play-session-screen__participant-role'>{t("STAFF", "Nhân viên Sacombank")}</div>
                    </div>

                  </div>
                  <CustomLiveAudioVisualizer
                    audioData={currentAudioFrameForVisualizer}
                    width={60}
                    height={40}
                    barWidth={4}
                    gap={2}
                    barColor="#1890ff"
                  />
                </div>
              </div>
            </div>
          </div>

        </div>
        <div className='max-w-[466px] w-[100%] px-6 py-4 bg-[#f3f5f9] flex gap-6 justify-between items-center rounded-3xl'>
          <div className='flex gap-2 items-center justify-start'>
            <AntButton
              type={BUTTON.TEXT}
              onClick={onToggleMic}
              disabled={sessionEnded || socketStatus !== SOCKET_STATUS.CONNECTED || !sessionStarted}
              className="role-play-session-screen__mic-button"
            >
              <img
                src={micEnabled ? AZURE_MIC : AZURE_STOP}
                alt={micEnabled ? t('DISABLE_MIC', 'Tắt mic') : t('ENABLE_MIC', 'Bật mic')}
                width="40"
                height="40"
              />
            </AntButton>
            <div className="role-play-session-screen__mic-status text-base font-normal text-black">
              {micEnabled ? t('MIC_ENABLED', 'Mic đang bật') : t('MIC_DISABLED_', 'Mic đang tắt')}
            </div>
          </div>
          <div className="role-play-session-screen__timer-container">
            <div className="role-play-session-screen__timer">{formatTime(elapsedTime)}</div>
          </div>
          <AntButton
            type={BUTTON.DEEP_RED}
            icon={<IconEndCall />}
            onClick={onEndSession}
            disabled={sessionEnded || socketStatus !== SOCKET_STATUS.CONNECTED || !sessionStarted}
            className="role-play-session-screen__exit-button"
          >
            {t("END_SESSION_", "Kết thúc")}
          </AntButton>
        </div>
        {transcript.length > 0 && (
          <div className='role-play-session-screen__chat-with-AI-footer m-0 py-6 px-6'>
            {transcript.length > 0 && <div className="role-play-session-screen__transcript">
              {transcript.length > 0 && transcript[transcript.length - 1].speaker === 'ai' && (
                <>
                  {!isAiInterrupted && (
                    <div className="role-play-session-screen__name-indicator">
                      {persona?.name || 'AI'}:
                    </div>
                  )}
                  <div className={`role-play-session-screen__current-message`}>
                    {transcript[transcript.length - 1].text}
                  </div>
                </>
              )}
              {transcript.length > 0 && transcript[transcript.length - 1].speaker === 'user' && (
                <div className="role-play-session-screen__current-message user-message">
                  <div className="role-play-session-screen__name-indicator">
                    {t('YOU_WILL_TALK_WITH_AI', 'Bạn')}:
                  </div>
                  <i>
                    {transcript[transcript.length - 1].text}
                  </i>
                </div>
              )}
              {isAiProcessing && !isAiSpeaking && !isAiInterrupted && (
                <div className="role-play-session-screen__ai-processing-indicator">
                  {(persona?.name || 'AI')} {t('IS_PROCESSING_AI_RESPONSE', 'đang suy nghĩ...')}
                </div>
              )}
            </div>}
            {mediaError && <div style={{ color: 'red', marginBottom: 16 }}>{t("ERROR_MEDIA", "Lỗi media:")} {mediaError}</div>}
          </div>
        )}
      </div>
      <div className='role-play-session-screen__show-name-conversation-with-AI'>
        <h2 class="m-0 text-[16px] font-semibold flex items-center gap-1">
          <span class="text-[18px]">💬</span>
          Chủ đề cuộc trò chuyện
        </h2>
        <div className='flex flex-col gap-4'>
          {scenariosDetail?.taskIds?.map((item, index) => (
            <div key={index} className='flex gap-2 py-[10px] px-4 rounded-xl bg-[#f1faff] content-container'>
              {/* <div className='text-[#10315d]'>•</div> */}
              <div className='text-[#10315d] leading-[24px]'>
                {item.name}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default NewSessionMainScreen;