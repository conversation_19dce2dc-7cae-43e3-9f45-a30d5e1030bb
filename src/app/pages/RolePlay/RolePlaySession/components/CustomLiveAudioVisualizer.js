import React, { useRef, useEffect } from 'react';

const CustomLiveAudioVisualizer = ({
  audioData, // Expects a Float32Array
  width = 300,
  height = 75,
  barWidth = 2,
  gap = 1,
  barColor = '#f76565',
  backgroundColor = 'transparent',
}) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    context.clearRect(0, 0, width, height);

    // Vẽ nền
    if (backgroundColor !== 'transparent') {
      context.fillStyle = backgroundColor;
      context.fillRect(0, 0, width, height);
    }

    // Ngưỡng im lặng
    const silenceThreshold = 0.005;
    let isEffectivelySilent = true;

    if (audioData && audioData.length > 0) {
      let maxAmplitude = 0;
      for (let k = 0; k < audioData.length; k++) {
        if (Math.abs(audioData[k]) > maxAmplitude) {
          maxAmplitude = Math.abs(audioData[k]);
        }
      }
      if (maxAmplitude >= silenceThreshold) {
        isEffectivelySilent = false;
      }
    }

    // Hàm vẽ hình chữ nhật bo góc
    const drawRoundedRect = (ctx, x, y, width, height, radius) => {
      const r = Math.min(radius, width / 2, height / 2);
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.lineTo(x + width - r, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + r);
      ctx.lineTo(x + width, y + height - r);
      ctx.quadraticCurveTo(x + width, y + height, x + width - r, y + height);
      ctx.lineTo(x + r, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - r);
      ctx.lineTo(x, y + r);
      ctx.quadraticCurveTo(x, y, x + r, y);
      ctx.closePath();
      ctx.fill();
    };

    if (isEffectivelySilent) {
      // Vẽ một đường thẳng ngang khi không có âm thanh
      const lineY = height / 2;
      context.beginPath();
      context.moveTo(0, lineY);
      context.lineTo(width, lineY);
      context.strokeStyle = barColor;
      context.lineWidth = 1;
      context.stroke();
    } else {
      // Vẽ các cột âm thanh có bo góc
      context.fillStyle = barColor;
      const numBars = Math.floor((width - gap) / (barWidth + gap));
      if (numBars <= 0) return;

      const dataPointsPerBar = Math.floor(audioData.length / numBars);

      for (let i = 0; i < numBars; i++) {
        let maxVal = 0;
        const startIndex = i * dataPointsPerBar;
        const endIndex = Math.min(startIndex + dataPointsPerBar, audioData.length);

        if (startIndex < endIndex) {
          for (let j = startIndex; j < endIndex; j++) {
            if (Math.abs(audioData[j]) > maxVal) {
              maxVal = Math.abs(audioData[j]);
            }
          }

          let barHeightValue = maxVal * height;

          if (barHeightValue > 0 && barHeightValue < 1) {
            barHeightValue = 1;
          }

          barHeightValue = Math.min(barHeightValue, height);

          if (barHeightValue > 0) {
            const x = gap + i * (barWidth + gap);
            const y = (height - barHeightValue) / 2;

            drawRoundedRect(context, x, y, barWidth, barHeightValue, 2); // radius = 2
          }
        }
      }
    }
  }, [audioData, width, height, barWidth, gap, barColor, backgroundColor]);

  return <canvas ref={canvasRef} width={width} height={height} />;
};

export default CustomLiveAudioVisualizer;
