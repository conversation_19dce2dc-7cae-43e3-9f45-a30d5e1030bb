import React, { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Spin, Alert, Typography, Descriptions, List, Tag, Divider, Button, Row, Col, Empty, Space, Avatar, Progress, Badge, Modal } from 'antd';
import { getRolePlaySessionDetail, getAnalysisForSession } from '@services/RolePlay/RolePlaySessionService';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import {
  ArrowLeftOutlined,
  ProfileOutlined,
  UserOutlined, MessageOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  BookOutlined,
} from '@ant-design/icons';
import AudioPlayer from '@src/app/component/AudioPlayer/AudioPlayer';
import AntButton from '@src/app/component/AntButton';
import { BUTTON } from '@constant';
import "./SessionResultScreen.scss";
import Work from "@src/asset/icon/course/Work.svg";
import Profile from "@src/asset/icon/course/Profile.svg";
import Calendar from "@src/asset/icon/course/Calendar.svg";
import Time_Circle from "@src/asset/icon/course/Time-Circle.svg";
import { getAIPersonaDetails } from '@src/app/services/RolePlay';
import CheckGreenBoder from '@src/app/component/SvgIcons/Check/CheckGreenBorder';

const { Title, Text, Paragraph } = Typography;

const SessionResultScreen = () => {
  const { sessionId, courseId: courseIdFromParams } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [sessionData, setSessionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const analysisCheckIntervalRef = useRef(null);
  const [persona, setPersona] = useState(null);

  // State for Transcript Modal
  const [isTranscriptModalVisible, setIsTranscriptModalVisible] = useState(false);

  // State for Task Detail Modal
  const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = useState(false);
  const [currentTaskForModal, setCurrentTaskForModal] = useState(null); // For Task details

  // State for Persona Detail Modal
  const [isPersonaDetailModalVisible, setIsPersonaDetailModalVisible] = useState(false);

  // State for Course Detail Modal
  const [isCourseDetailModalVisible, setIsCourseDetailModalVisible] = useState(false);

  useEffect(() => {
    if (!sessionId) {
      console.warn('SessionId is undefined.');
      setError(t('SESSION_ID_UNDEFINED', 'Không tìm thấy mã phiên.'));
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const fetchData = async () => {
      try {
        const data = await getRolePlaySessionDetail(sessionId);
        console.log('data', data);

        if (data) {
          setSessionData(data);

          if (data.status === 'analyzed' && data.analysisId?.result) {
            console.log('Analysis already available:', data.analysisId.result);
          } else {
            startAnalysisPolling(sessionId);
          }

          const personaDetails = await getAIPersonaDetails(data.personaId);
          if (personaDetails) {
            setPersona(personaDetails);
          } else {
            message.warning(t('NO_PERSONA_ASSIGNED', 'Khóa học này chưa được gán AI Persona.'));
          }
        } else {
          setError(t('ERROR_FETCHING_SESSION_DETAIL', 'Không thể tải chi tiết phiên. Dữ liệu không hợp lệ.'));
        }
      } catch (err) {
        console.error('Error fetching session detail:', err);
        setError(t('ERROR_FETCHING_SESSION_DETAIL_NETWORK', 'Lỗi khi tải chi tiết phiên. Vui lòng thử lại.'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    return () => {
      if (analysisCheckIntervalRef.current) {
        clearInterval(analysisCheckIntervalRef.current);
        analysisCheckIntervalRef.current = null;
      }
    };
  }, [sessionId, t]);

  /**
   * Start polling for analysis results every 3 seconds
   * @param {string} sessionId - The ID of the session to check for analysis
   */
  const startAnalysisPolling = (sessionId) => {
    // Clear any existing interval
    if (analysisCheckIntervalRef.current) {
      clearInterval(analysisCheckIntervalRef.current);
    }

    // Set up new interval to check every 3 seconds
    analysisCheckIntervalRef.current = setInterval(() => {
      checkSessionAnalysis(sessionId);
    }, 3000); // 3 seconds
  };

  /**
   * Check if analysis results are available for the session
   * @param {string} sessionId - The ID of the session to check
   */
  const checkSessionAnalysis = (sessionId) => {
    console.log('Checking session analysis for:', sessionId);
    getAnalysisForSession(sessionId, false) // false to avoid showing loading indicator
      .then(data => {
        if (data && data.result) {
          console.log('Analysis results received:', data);
          // Stop polling once we have results
          if (analysisCheckIntervalRef.current) {
            clearInterval(analysisCheckIntervalRef.current);
            analysisCheckIntervalRef.current = null;
          }

          // Update session data with the analysis results
          setSessionData(prevData => {
            if (!prevData) return null;
            return {
              ...prevData,
              analysisId: data,
              status: 'analyzed'
            };
          });
        } else {
          console.log('Analysis not ready yet, will check again in 3 seconds');
        }
      })
      .catch(err => {
        console.error('Error checking session analysis:', err);
      });
  };

  // useEffect for fetching Analysis Data is REMOVED - Analysis data comes from sessionData.analysisId

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert message={t('ERROR_TITLE', 'Lỗi')} description={error} type="error" showIcon />
        <AntButton type={BUTTON.TEXT} onClick={() => navigate(-1)} style={{ marginTop: '20px' }} icon={<ArrowLeftOutlined />}>
          {t('GO_BACK', 'Quay lại')}
        </AntButton>
      </div>
    );
  }

  if (!sessionData) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <Empty description={t('NO_SESSION_DATA_FOUND', 'Không tìm thấy dữ liệu cho phiên này.')} />
        <AntButton type={BUTTON.TEXT} onClick={() => navigate(-1)} style={{ marginTop: '20px' }} icon={<ArrowLeftOutlined />}>
          {t('GO_BACK', 'Quay lại')}
        </AntButton>
      </div>
    );
  }

  const {
    taskIds: sessionTaskObjectsFromData, // Renamed to avoid conflict
    courseId: sessionCourseInfoFromData, // Renamed
    transcripts: transcriptsFromData,     // Renamed
    status: sessionStatusFromData,        // Renamed
    endTime: sessionEndTimeFromData,      // Renamed
    analysisId: analysisIdFromData,       // Renamed
    // studentId: studentIdFromData, // If needed directly, rename too
    // createdBy: createdByFromData, // If needed directly, rename too
    // ... any other direct destructured props from sessionData that might conflict
  } = sessionData;

  // Use the renamed variables for subsequent logic
  const mainTaskObjectForDisplay = sessionTaskObjectsFromData?.find(t => t._id === sessionData.taskId); // sessionData.taskId is fine if not destructured and renamed
  const personaInfo = persona;
  const finalCourseIdForNavigation = courseIdFromParams || sessionCourseInfoFromData?._id;

  const showTranscriptModal = () => {
    setIsTranscriptModalVisible(true);
  };

  const handleTranscriptModalClose = () => {
    setIsTranscriptModalVisible(false);
  };

  const showTaskDetailModal = (taskObject) => {
    setCurrentTaskForModal(taskObject);
    setIsTaskDetailModalVisible(true);
  };

  const handleTaskDetailModalClose = () => {
    setIsTaskDetailModalVisible(false);
    setCurrentTaskForModal(null);
  };

  const showPersonaDetailModal = () => {
    setIsPersonaDetailModalVisible(true);
  };

  const handlePersonaDetailModalClose = () => {
    setIsPersonaDetailModalVisible(false);
  };

  const showCourseDetailModal = () => {
    setIsCourseDetailModalVisible(true);
  };

  const handleCourseDetailModalClose = () => {
    setIsCourseDetailModalVisible(false);
  };

  // Helper to render analysis sections
  const renderAnalysisSection = () => {
    if (!sessionData) return null;

    // Use the renamed destructured variables directly, or access them via sessionData.analysisId.result etc.
    if (sessionStatusFromData === 'analyzed' && analysisIdFromData && analysisIdFromData.result) {
      const { result } = analysisIdFromData; // result is local to this function, so it's fine

      return (
        <div className='flex gap-6'>
          <div className='flex flex-col gap-10 max-w-[760px] w-[100%]'>
            <div className='flex w-[100%]'>
              <div className='h-auto w-1 bg-[#0c4da2]'></div>
              <div className='bg-[#f2faff] px-6 py-4 flex flex-col gap-4 w-[100%]'>
                <div className='text-lg leading-[32px] font-semibold text-[#0c4da2]'>
                  {t("INFORMATION_TITLE", "Thông tin chung")}
                </div>
                <div className='h-[1px] w-auto bg-[#cdd5df]'></div>
                <div className='flex flex-col gap-4'>
                  <div className='flex gap-4'>
                    <div className='flex gap-2 items-start max-w-[161px] w-[100%]'>
                      <img src={Work} alt='' />
                      <span className='leading-[24px] font-semibold'>{t("COURSE_INFO_LABEL", "Khóa học:")}</span>
                    </div>
                    <div className='flex gap-2 items-start'>
                      {sessionData?.courseId?.name}
                      {/* <div className='mt-[1px] cursor-pointer' onClick={showCourseDetailModal}>
                        <InfoCircleOutlined />
                      </div>                      */}
                    </div>
                  </div>
                  <div className='flex gap-4'>
                    <div className='flex gap-2 items-center max-w-[161px] w-[100%]'>
                      <img src={Profile} alt='' />
                      <span className='leading-[24px] font-semibold'>{t("PERSONA_INFO_LABEL", "AI Persona:")}</span>
                    </div>
                    <div className='flex gap-2 items-center'>
                      {persona?.name}
                      <div className='mt-[1px] cursor-pointer' onClick={showPersonaDetailModal}>
                        <InfoCircleOutlined />
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-4'>
                    <div className='flex gap-2 items-center'>
                      <img src={Time_Circle} alt='' />
                      <span className='leading-[24px] font-semibold'>{t("TRANSCRIPT_LABEL", "Lịch sử hội thoại:")}</span>
                    </div>
                    <div className='flex gap-2 items-center'>
                      {transcriptsFromData?.length} {t("TIMES_STB", "lượt")}
                      <div className='mt-[1px] cursor-pointer' onClick={showTranscriptModal}>
                        <MessageOutlined />
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-4'>
                    <div className='flex gap-2 items-center max-w-[161px] w-[100%]'>
                      <img src={Calendar} alt='' />
                      <span className='leading-[24px] font-semibold'>{t("SESSION_TIME_LABEL", "Thời gian:")}</span>
                    </div>
                    <div className='flex gap-2 items-center'>
                      {sessionEndTimeFromData ? moment(sessionEndTimeFromData).format('DD/MM/YYYY') : t('N/A', 'N/A')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='flex flex-col gap-2 w-[100%]'>
              <div className='text-lg leading-[24px] font-semibold text-[#0c4da2]'>
                {t("FEEDBACK_FROM_AI_TRAINER_TITLE", "Phản hồi từ AI Trainer")}
              </div>
              <div className='flex flex-col leading-[24px]'>
                <span>
                  {result?.trainerFeedback?.generalComments}
                </span>
                {!!result?.trainerFeedback?.improvementSuggestions?.length && (
                  <span className='font-semibold mt-1'>{t("IMPROVEMENT_SUGGESTIONS_LABEL", "Đề xuất cải thiện:")} </span>
                )}
                <ul className='m-0 pl-3 list-disc list-inside'>
                  {result?.trainerFeedback?.improvementSuggestions?.map((item, index) => (
                    <li className='m-0' key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </div>
            <div className='flex w-[100%]'>
              <div className='h-auto w-1 bg-[#0c4da2]'></div>
              <div className='bg-[#f2faff] px-6 py-4 flex flex-col w-[100%]'>
                <div className='text-lg leading-[32px] font-semibold text-[#0c4da2]'>
                  {t("ANALYSIS_KNOWLEDGE_TITLE", "Phân tích kiến thức")}
                </div>
                {/* <div className='h-[1px] w-auto bg-[#cdd5df]'></div> */}
                <div>
                  {result?.knowledgeAnalysis?.taskAnalyses?.map((item, index) => {
                    return (
                      <div key={item.taskId} className=''>
                        <div className='h-[1px] w-auto bg-[#cdd5df] my-4'></div>
                        <div>
                          <div className='flex justify-between items-start'>
                            <span className='leading-[24px] font-semibold'>
                              {index + 1}. {item.taskName}
                            </span>
                            <div className='px-2 py-0.5 rounded-2xl bg-[#e4dffe] text-[#4d45be] flex items-center justify-start gap-1 max-w-[105px] w-[100%]'>
                              <CheckGreenBoder width="17" height="16" fill='#4d45be' />
                              <span>{t("SCORE_LABEL", "Điểm:")} {item.score}</span>
                            </div>
                          </div>
                          <div className='mt-2 leading-[24px] text-[#10315d]'>
                            {item.details}
                          </div>
                          <div className='mt-2'>
                            <div className='leading-[24px] text-[#10315d]'>
                              <span className='font-semibold'>{t("DECIDING_FACTOR_LABEL", "Yếu tố quyết định:")} </span>
                              <ul className='m-0 pl-3 list-disc list-inside'>
                                {item.makeOrBreak?.map((factor, index) => (
                                  <li className='m-0' key={index}>{factor}</li>
                                ))}
                              </ul>
                            </div>
                            <div className='leading-[24px] text-[#10315d]'>
                              <span className='font-semibold'>{t("IMPROVEMENT_SUGGESTIONS_LABEL", "Đề xuất cải thiện:")} </span>
                              <ul className='m-0 pl-3 list-disc list-inside'>
                                {item.suggestions?.map((factor, index) => (
                                  <li className='m-0' key={index}>{factor}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
                <div className='session-result-screen__proficiencyProcess'>
                  {result?.knowledgeAnalysis?.proficiencyProcess}
                </div>
              </div>
            </div>
          </div>
          <div className='max-w-[360px] w-[100%]'>
            <div className='session-result-screen__final-score'>
              <strong className='text-center'>{t('FINAL_SCORE', 'ĐIỂM AI')}</strong>
              <h1 className='text-[50px] font-bold text-center m-0 text-[#0c4da2]'>
                {result?.simulationScore}
              </h1>
              <div className='flex justify-center '>
                <div className='flex items-center gap-1 px-2 py-0.5 rounded-2xl bg-[#e4dffe] text-[#4d45be]'>
                  <CheckGreenBoder width="17" height="16" fill='#4d45be' />
                  <span>{t('PASS_SCORE_DYNAMIC_AI', 'Điểm yêu cầu: {0}').format(sessionCourseInfoFromData?.passScore || 70)}</span>
                </div>
              </div>
              <div className='h-[1px] w-auto bg-[#cdd5df]'></div>
              <div className='text-[#10315d] leading-[24px]'>
                {result?.summary}
              </div>
              <div className='flex flex-col gap-4'>
                {result?.topInsights?.map((item, index) => (
                  <div key={index} className='flex gap-2 py-[10px] px-4 rounded-xl bg-[#f1faff]'>
                    <div className='text-[#10315d]'>•</div>
                    <div className='text-[#10315d] leading-[24px]'>
                      {item}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    } else if (sessionStatusFromData !== 'analyzed') {
      return (
        <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <Paragraph style={{ marginTop: 16, textAlign: 'center' }}>
            {t('ANALYSIS_LOADING_TITLE', 'Đang phân tích kết quả phiên luyện tập...')}
          </Paragraph>
          <Paragraph type="secondary" style={{ textAlign: 'center' }}>
            {t('ANALYSIS_LOADING_DESCRIPTION', 'Quá trình này có thể mất vài phút, vui lòng đợi.')}
          </Paragraph>
        </div>
      );
    } else {
      // Status is 'analyzed' but analysisIdFromData or analysisIdFromData.result is missing
      return <Empty description={t('NO_ANALYSIS_DATA_AVAILABLE', 'Không có dữ liệu phân tích cho phiên này.')} style={{ marginTop: 20 }} />;
    }
  };

  return (
    <div className='session-result-screen'>
      <div className="go-back-button">
        <AntButton
          icon={<ArrowLeftOutlined style={{ color: "#0c4da2" }} />}
          onClick={() => navigate(finalCourseIdForNavigation ? `/role-play/session/${finalCourseIdForNavigation}` : -1)}
          type={BUTTON.TEXT}
          className="role-play-session-screen__back-button"
        >
          {t('BACK_TO_COURSE', 'Quay lại khóa học')}
        </AntButton>
      </div>
      <Card
        title={
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                {t('SESSION_RESULT_TITLE_AI', 'Kết quả trò chuyện AI Coach')}
              </Title>
            </Col>
          </Row>
        }
        bordered={false}
      >
        <Modal
          title={<><ProfileOutlined style={{ marginRight: 8 }} /> {currentTaskForModal?.name || t('TASK_DETAILS_MODAL_TITLE', 'Chi tiết Nhiệm Vụ')}</>}
          open={isTaskDetailModalVisible}
          onOk={handleTaskDetailModalClose}
          onCancel={handleTaskDetailModalClose}
          width={700}
          footer={[
            <AntButton key="close" type={BUTTON.TEXT} onClick={handleTaskDetailModalClose}>
              {t('CLOSE_MODAL', 'Đóng')}
            </AntButton>,
          ]}
        >
          <Card type="inner" title={t('TASK_INFORMATION', 'Thông tin Nhiệm Vụ')} style={{ border: 0, boxShadow: 'none' }}>
            <Descriptions column={1} bordered>
              <Descriptions.Item label={t('TASK_NAME_LABEL', 'Tên nhiệm vụ')}>{currentTaskForModal?.name || t('N/A', 'N/A')}</Descriptions.Item>
              <Descriptions.Item label={t('DESCRIPTION', 'Mô tả')}>
                <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{currentTaskForModal?.description || t('N/A', 'N/A')}</pre>
              </Descriptions.Item>
              <Descriptions.Item label={t('EVALUATION_GUIDELINES', 'Hướng dẫn đánh giá')}>
                <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>
                  {currentTaskForModal?.evaluationGuidelines || t('N/A', 'N/A')}
                </pre>
              </Descriptions.Item>
            </Descriptions>
            {currentTaskForModal?.exampleVideoUrl && (
              <Paragraph style={{ marginTop: 16 }}>
                <strong>{t('EXAMPLE_VIDEO', 'Video mẫu')}:</strong> <a href={currentTaskForModal.exampleVideoUrl} target="_blank" rel="noopener noreferrer">{currentTaskForModal.exampleVideoUrl}</a>
              </Paragraph>
            )}
            {currentTaskForModal?.helpfulLinks && currentTaskForModal.helpfulLinks.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Paragraph strong>{t('HELPFUL_LINKS', 'Liên kết hữu ích')}:</Paragraph>
                <List
                  size="small"
                  bordered
                  dataSource={currentTaskForModal.helpfulLinks}
                  renderItem={(link, index) => (
                    <List.Item key={index}><a href={link} target="_blank" rel="noopener noreferrer">{link}</a></List.Item>
                  )}
                />
              </div>
            )}
            {!currentTaskForModal?.description && !currentTaskForModal?.evaluationGuidelines && !currentTaskForModal?.exampleVideoUrl && (!currentTaskForModal?.helpfulLinks || currentTaskForModal?.helpfulLinks.length === 0) && (
              <Empty description={t('NO_TASK_DETAILS_AVAILABLE', 'Không có chi tiết cho nhiệm vụ này.')} />
            )}
          </Card>
        </Modal>

        {/* AI Persona Detail Modal */}
        {personaInfo && (
          <Modal
            title={<><UserOutlined style={{ marginRight: 8 }} /> {t('PERSONA_DETAILS_TITLE', 'Chi tiết AI Persona')}</>}
            open={isPersonaDetailModalVisible}
            onOk={handlePersonaDetailModalClose}
            onCancel={handlePersonaDetailModalClose}
            width={600}
            footer={[
              <AntButton key="close" type={BUTTON.TEXT} onClick={handlePersonaDetailModalClose}>
                {t('CLOSE_', 'Đóng')}
              </AntButton>,
            ]}
          >
            <Card type="inner" title={`${t('PERSONA_INFO', 'Thông tin về')} ${personaInfo.name} ${personaInfo.role ? `(${personaInfo.role})` : ''}`} style={{ border: 0, boxShadow: 'none', padding: "0" }} className='AI-Persona-Detail-Modal'>
              <Descriptions column={1} bordered size="small" layout="vertical" className='AI-Persona-Detail-Modal__descriptions'>
                <Descriptions.Item label={t('MOOD_', 'Tâm trạng')}>{personaInfo.mood || t('N/A', 'N/A')}</Descriptions.Item>
                <Descriptions.Item label={t('ORGANIZATION_', 'Tổ chức')}>{personaInfo.organization || t('N/A', 'N/A')}</Descriptions.Item>
                <Descriptions.Item label={t('BACKGROUND_', 'Bối cảnh')}><pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{personaInfo.personaBackground || t('N/A', 'N/A')}</pre></Descriptions.Item>
                <Descriptions.Item label={t('CONCERN_', 'Mối quan tâm')}><pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{personaInfo.personaConcern || t('N/A', 'N/A')}</pre></Descriptions.Item>
              </Descriptions>
            </Card>
          </Modal>
        )}

        {/* Course Detail Modal */}
        <Modal
          title={<><BookOutlined style={{ marginRight: 8 }} /> {t('COURSE_DETAILS_TITLE', 'Chi Tiết Khóa Học')}</>}
          open={isCourseDetailModalVisible}
          onOk={handleCourseDetailModalClose}
          onCancel={handleCourseDetailModalClose}
          width={700}
          footer={[
            <AntButton key="close" type={BUTTON.TEXT} onClick={handleCourseDetailModalClose}>
              {t('CLOSE', 'Đóng')}
            </AntButton>,
          ]}
        >
          {sessionCourseInfoFromData ? (
            <Card type="inner" title={t('COURSE_INFORMATION', 'Thông tin Khóa Học')} style={{ border: 0, boxShadow: 'none' }}>
              <Title level={5}>{sessionCourseInfoFromData.name || t('N/A', 'N/A')}</Title>
              {sessionCourseInfoFromData.description && (
                <Paragraph style={{ marginTop: 8 }}>
                  <strong>{t('COURSE_DESCRIPTION_LABEL', 'Mô tả khóa học')}:</strong>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{sessionCourseInfoFromData.description}</pre>
                </Paragraph>
              )}
              {/* Display tasks within the course */}
              {sessionTaskObjectsFromData && sessionTaskObjectsFromData.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Paragraph strong>{t('TASKS_IN_THIS_COURSE_LABEL', 'Các nhiệm vụ trong khóa học này')}:</Paragraph>
                  <List
                    size="small"
                    bordered
                    dataSource={sessionTaskObjectsFromData}
                    renderItem={taskItem => (
                      <List.Item key={taskItem._id}>
                        <List.Item.Meta
                          title={taskItem.name}
                          description={<pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{taskItem.description}</pre>}
                        />
                        {/* Optionally show button to view this task's details from analysis if available */}
                      </List.Item>
                    )}
                  />
                </div>
              )}
              {(sessionCourseInfoFromData.referenceUrls || sessionCourseInfoFromData.referenceMaterial) && (sessionCourseInfoFromData.referenceUrls?.length > 0 || sessionCourseInfoFromData.referenceMaterial?.length > 0) && (
                <div style={{ marginTop: 16 }}>
                  <Paragraph strong>{t('REFERENCE_MATERIAL_LABEL', 'Tài liệu tham khảo')}:</Paragraph>
                  <List
                    size="small"
                    bordered
                    dataSource={[...(sessionCourseInfoFromData.referenceUrls || []), ...(sessionCourseInfoFromData.referenceMaterial || [])]}
                    renderItem={(item, index) => {
                      const url = typeof item === 'string' ? item : item?.url;
                      const text = typeof item === 'string' ? item : (item?.name || item?.title || url);
                      if (!url) return <List.Item key={index}>{t('INVALID_MATERIAL_ITEM', 'Tài liệu không hợp lệ')}</List.Item>;
                      return (
                        <List.Item key={index}>
                          <a href={url} target="_blank" rel="noopener noreferrer">{text}</a>
                        </List.Item>
                      );
                    }}
                  />
                </div>
              )}
              {!sessionCourseInfoFromData.description && (!sessionTaskObjectsFromData || sessionTaskObjectsFromData.length === 0) && (!sessionCourseInfoFromData.referenceUrls || sessionCourseInfoFromData.referenceUrls.length === 0) && (!sessionCourseInfoFromData.referenceMaterial || sessionCourseInfoFromData.referenceMaterial.length === 0) && (
                <Paragraph>{t('NO_COURSE_DETAILS_AVAILABLE', 'Không có chi tiết bổ sung cho khóa học này.')}</Paragraph>
              )}
            </Card>
          ) : (
            <Empty description={t('NO_COURSE_INFO_LOADED', 'Không thể tải thông tin khóa học.')} />
          )}
        </Modal>

        {/* Transcript Modal */}
        <Modal
          title={<><MessageOutlined style={{ marginRight: 8 }} /> {t('TRANSCRIPT_TITLE', 'Lịch sử hội thoại')}</>}
          open={isTranscriptModalVisible}
          onOk={handleTranscriptModalClose}
          onCancel={handleTranscriptModalClose}
          width={800}
          footer={[
            <AntButton key="close" type={BUTTON.TEXT} onClick={handleTranscriptModalClose}>
              {t('CLOSE_', 'Đóng')}
            </AntButton>,
          ]}
        >
          {transcriptsFromData && transcriptsFromData.length > 0 ? (
            <List
              itemLayout="horizontal"
              dataSource={transcriptsFromData}
              renderItem={(item, index) => (
                <List.Item key={item._id || index} style={{ alignItems: 'flex-start' }}>
                  <List.Item.Meta
                    // Bỏ avatar ở đây
                    title={
                      <div className="flex items-center gap-2">
                        <Tag color={item.role === 'student' ? 'blue' : 'green'}>
                          {item.role === 'student' ? t('YOU_', 'Bạn') : (personaInfo?.name || t('AI', 'AI'))}
                        </Tag>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {moment(item.timestamp).format('HH:mm:ss, DD/MM/YYYY')}
                        </Text>
                      </div>
                    }
                    description={
                      <>
                        <Paragraph style={{ marginBottom: '5px', whiteSpace: 'pre-wrap', marginTop:"10px", marginLeft:"2px" }}>{item.content}</Paragraph>
                        {item.audioId && (
                          <AudioPlayer audioId={item.audioId} smallPlayer />
                        )}
                      </>
                    }
                  />
                </List.Item>
              )}
              style={{
                background: '#fff',
                padding: '0 16px',
                borderRadius: '8px',
                maxHeight: '60vh',
                overflowY: 'auto',
              }}
            />
          ) : (
            <Empty description={t('NO_TRANSCRIPT_AVAILABLE', 'Không có lịch sử hội thoại.')} />
          )}
        </Modal>
        {renderAnalysisSection()}
      </Card>
    </div>
  );
};

export default SessionResultScreen;
