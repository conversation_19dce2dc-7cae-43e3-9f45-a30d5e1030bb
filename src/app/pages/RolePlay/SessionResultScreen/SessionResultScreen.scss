.session-result-screen {
  padding: 24px 0px;
  margin-bottom: -32px;
  margin-left: -16px;
  margin-right: -16px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100vw - 6px);
  min-height: calc(100vh - 56px);

  .go-back-button {
    max-width: 1162px;
    width: 100%;

    .ant-btn {
      padding: 0;
      border: none;

      &:hover,
      &:focus {
        background: transparent !important;
        color: #0c4da2 !important;
      }
    }

    .role-play-session-screen__back-button {
      flex-shrink: 0;
      color: #0c4da2 !important;
      font-size: 14px;
      line-height: 18px;
      font-weight: 400;
      text-align: center;
      padding: 0 8px;
      border-radius: 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 0 8px rgba(#0c4da2, 0.2);
      };
    }
  }

  &__proficiencyProcess {
    width: 100%;
    margin-top: 16px;
    border: 1px dashed #0c4da2;
    padding: 16px;
    border-radius: 12px;
    color: #10315d;
    background-color: #a4deff;
    font-style: italic;
  }

  &__final-score {
    max-width: 360px;
    width: 100%;
    padding: 24px;
    border-radius: 16px;
    background-color: #fff;
    box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: -72px;
    // justify-content: center;
  }

  .ant-card {
    max-width: 1144px;
    width: 100%;
    border: none;
    box-shadow: none !important;
    margin-top: 24px;

    .ant-card-head {
      border-bottom: none !important;
      min-height: 32px;
      padding: 0;

      .ant-card-head-wrapper {
        .ant-typography {
          font-size: 24px;
          line-height: 32px;
          font-weight: 600;
        }
      }
    }

    .ant-card-body {
      padding: 0;
      margin-top: 40px;
    }
  }

}

.AI-Persona-Detail-Modal {
  .ant-card-head {
    background-color: #f2faff;
    color: #0c4da2;
  }

  .ant-card-body {
    padding: 24px 0;




  }
}

.AI-Persona-Detail-Modal__descriptions {
  .ant-descriptions-item-label {
    background-color: #f2faff !important;
    color: #0c4da2 !important;
  }

  .ant-descriptions-item-content {
    padding: 10px 16px !important;
    font-family: 'Segoe UI';
  }
}