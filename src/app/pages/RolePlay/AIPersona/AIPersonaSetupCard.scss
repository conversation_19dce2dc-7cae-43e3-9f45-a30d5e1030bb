.ai-persona-setup-card {
  &__section-title {
    margin-bottom: 16px !important;
    font-size: 17px;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: 0.3px;
  }

  &__character-section,
  &__background-section,
  &__concerns-section {
    margin-top: 24px;

    &:first-child {
      margin-top: 0;
    }
  }

  &__details-content {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__avatar-container {
    flex-shrink: 0;
  }

  &__avatar {
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &__details-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__name-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  &__name {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
    line-height: 1.3;
  }

  &__age {
    font-size: 14px;
    font-weight: 500;
    color: #595959;
    background: #f0f2f5;
    padding: 3px 10px;
    border-radius: 12px;
    border: 1px solid #e8e8e8;
  }

  &__info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  &__info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .ai-persona-setup-card__label {
      font-size: 11px;
      font-weight: 600;
      color: #8c8c8c;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .ai-persona-setup-card__value {
      font-size: 15px;
      font-weight: 600;
      color: #262626;
      line-height: 1.4;
    }
  }

  &__character-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }

  &__character-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #1890ff;
  }

  &__label {
    font-size: 12px;
    font-weight: 600;
    color: #595959;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 2px;
  }

  &__value {
    font-size: 15px;
    font-weight: 600;
    color: #262626;
    line-height: 1.4;
  }

  &__content-box {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border-left: 3px solid #52c41a;
  }

  &__paragraph {
    margin: 0 !important;
    line-height: 1.7;
    color: #434343;
    font-size: 15px;
    font-weight: 400;
    letter-spacing: 0.2px;
  }

  // Responsive design
  @media (max-width: 768px) {
    &__details-content {
      flex-direction: column;
      gap: 16px;
      margin-bottom: 20px;
      padding-bottom: 16px;
    }

    &__avatar-container {
      align-self: center;
    }

    &__info-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    &__character-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    &__name-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    &__character-section,
    &__background-section,
    &__concerns-section {
      margin-top: 20px;
    }
  }

  @media (max-width: 480px) {
    &__details-content {
      margin-bottom: 16px;
      padding-bottom: 12px;
    }

    &__character-section,
    &__background-section,
    &__concerns-section {
      margin-top: 16px;
    }
  }
}
