import React from 'react';
import {Card, Avatar, Typography, Divider, Space} from 'antd';
import AntButton from '@component/AntButton';
import {BUTTON} from '@constant';
import {EditOutlined, BookOutlined} from '@ant-design/icons'; // Example icons
import {useTranslation} from 'react-i18next';
import './AIPersonaSetupCard.scss';
import {API} from '@api';
const {Title, Text, Paragraph} = Typography;

// Mock data based on the image and PRD for standalone testing
const defaultPersona = {};

export const AIPersonaSetupCard = ({persona = defaultPersona, onEdit, onLearnMore, hideEditButton = false}) => {
  const {t} = useTranslation();

  const handleEdit = () => {
    if (onEdit) {
      onEdit(persona);
    }
    console.log('Edit persona:', persona?.name);
  };

  const handleLearnMore = () => {
    if (onLearnMore) {
      onLearnMore();
    }
    console.log('Learn more about AI Persona Setup');
  };

  return (
    <div className="ai-persona-setup-card">
      <div className="ai-persona-setup-card__details-content">
        <div className="ai-persona-setup-card__avatar-container">
          <Avatar
            size={80}
            src={API.STREAM_ID.format(persona?.avatarId)}
            className="ai-persona-setup-card__avatar"
          />
        </div>
        <div className="ai-persona-setup-card__details-text">
          <div className="ai-persona-setup-card__name-section">
            <Text strong className="ai-persona-setup-card__name">
              {persona?.name || t('NAME_NOT_SET', 'Name not set')}
            </Text>
            {persona?.age && (
              <Text type="secondary" className="ai-persona-setup-card__age">
                {persona?.age} {t('YEARS_OLD', 'tuổi')}
              </Text>
            )}
          </div>
          <div className="ai-persona-setup-card__info-grid">
            <div className="ai-persona-setup-card__info-item">
              <Text type="secondary" className="ai-persona-setup-card__label">
                {t('ROLE_LABEL', 'Vai trò')}
              </Text>
              <Text className="ai-persona-setup-card__value">
                {persona?.role || t('NOT_SET', 'Chưa thiết lập')}
              </Text>
            </div>
            <div className="ai-persona-setup-card__info-item">
              <Text type="secondary" className="ai-persona-setup-card__label">
                {t('ORGANIZATION_LABEL', 'Tổ chức')}
              </Text>
              <Text className="ai-persona-setup-card__value">
                {persona?.organization || t('NOT_SET', 'Chưa thiết lập')}
              </Text>
            </div>
            {persona?.gender && (
              <div className="ai-persona-setup-card__info-item">
                <Text type="secondary" className="ai-persona-setup-card__label">
                  {t('GENDER_LABEL', 'Giới tính')}
                </Text>
                <Text className="ai-persona-setup-card__value">
                  {persona?.gender === 'male' ? t('MALE', 'Nam') :
                   persona?.gender === 'female' ? t('FEMALE', 'Nữ') :
                   t('OTHER', 'Khác')}
                </Text>
              </div>
            )}
          </div>
        </div>
      </div>

      {(persona?.mood || (persona?.smallTalkLikely !== undefined && persona?.smallTalkLikely !== null)) && (
        <div className="ai-persona-setup-card__character-section">
          <Title level={5} className="ai-persona-setup-card__section-title">
            {t('AI_PERSONA_CHARACTER_TITLE', 'Đặc điểm tính cách')}
          </Title>
          <div className="ai-persona-setup-card__character-grid">
            {persona?.mood && (
              <div className="ai-persona-setup-card__character-item">
                <Text type="secondary" className="ai-persona-setup-card__label">
                  {t('MOOD_LABEL', 'Tâm trạng')}
                </Text>
                <Text className="ai-persona-setup-card__value">
                  {persona?.mood}
                </Text>
              </div>
            )}
            {persona?.smallTalkLikely !== undefined && persona?.smallTalkLikely !== null && (
              <div className="ai-persona-setup-card__character-item">
                <Text type="secondary" className="ai-persona-setup-card__label">
                  {t('AI_PERSONA_SMALL_TALK_LIKELY', 'Khả năng nói chuyện phiếm')}
                </Text>
                <Text className="ai-persona-setup-card__value">
                  {persona?.smallTalkLikely}%
                </Text>
              </div>
            )}
          </div>
        </div>
      )}

      {persona?.personaBackground && (
        <div className="ai-persona-setup-card__background-section">
          <Title level={5} className="ai-persona-setup-card__section-title">
            {t('AI_PERSONA_BACKGROUND_TITLE', 'Thông tin nền tảng')}
          </Title>
          <div className="ai-persona-setup-card__content-box">
            <Paragraph className="ai-persona-setup-card__paragraph">
              {persona?.personaBackground}
            </Paragraph>
          </div>
        </div>
      )}

      {persona?.personaConcern && (
        <div className="ai-persona-setup-card__concerns-section">
          <Title level={5} className="ai-persona-setup-card__section-title">
            {t('AI_PERSONA_CONCERNS_TITLE', 'Mối quan tâm')}
          </Title>
          <div className="ai-persona-setup-card__content-box">
            <Paragraph className="ai-persona-setup-card__paragraph">
              {persona?.personaConcern}
            </Paragraph>
          </div>
        </div>
      )}
    </div>
  );
};
