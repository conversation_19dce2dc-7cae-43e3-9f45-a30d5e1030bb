.user-management-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .user-management-info-card,
  .user-management-search-card,
  .user-management-table-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .user-management-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .user-management-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .user-management-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-user {
      margin-left: auto;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      // Search buttons styling is now handled by global admin-filter-buttons class
    }
  }

  .user-management-table-card {
    .ant-table {
      .ant-table-container {
        .ant-table-thead > tr > th {
          background: #fafafa;
        }
      }
    }

    // Action buttons styling is now handled by global action-buttons class in buttons.scss

    .user-name-cell {
      .user-name {
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 2px;
      }

      .user-email {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
