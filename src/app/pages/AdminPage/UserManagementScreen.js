import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip, Avatar } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, UserOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, navigateAfterDelete, orderColumn, paginationConfig, handleReplaceUrlSearch, formatDate } from "@common/functionCommons";

import { getAllUsers, deleteUser, createUser, updateUserInfo } from "@services/UserManagement";

import CreateUserModal from "@app/pages/AdminPage/UserManagement/components/CreateUserModal";
import EditUserModal from "@app/pages/AdminPage/UserManagement/components/EditUserModal";

import "./UserManagementScreen.scss";

// Options cho role filter
const ROLE_OPTIONS = [
  { value: "admin", label: "Admin" },
  { value: "normal", label: "Normal" },
  { value: "contributor", label: "Contributor" }
];

// Options cho type filter
const TYPE_OPTIONS = [
  { value: "student", label: "Student" },
  { value: "teacher", label: "Admin" }
];

// Options cho status filter
const STATUS_OPTIONS = [
  { value: true, label: "Active" },
  { value: false, label: "Inactive" }
];

const UserManagementScreen = ({ ...props }) => {
  const { t } = useTranslation();
  const location = useLocation();

  const [usersData, setUsersData] = useState(PAGINATION_INIT);
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isShowCreateModal, setShowCreateModal] = useState(false);
  const [isShowEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    fetchUsersData(paging, query);
  }, [location.search]);

  async function fetchUsersData(
    paging = usersData.paging,
    query = usersData.query,
  ) {
    setIsLoading(true);
    const searchFields = ["fullName", "email"];
    const apiResponse = await getAllUsers(paging, query, searchFields);
    if (apiResponse) {
      setUsersData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  }

  async function handleDeleteUser(userId, userName) {
    confirm.delete({
      title: t("DELETE_USER"),
      content: t("DELETE_USER_CONFIRM", { name: userName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        try {
          const apiResponse = await deleteUser(userId);
          if (apiResponse) {
            toast.success(t("DELETE_USER_SUCCESS"));
            navigateAfterDelete(usersData);
            fetchUsersData();
          } else {
            toast.error(t("DELETE_USER_ERROR"));
          }
        } catch (error) {
          toast.error(t("DELETE_USER_ERROR"));
        } finally {
          setIsLoading(false);
        }
      },
    });
  }

  async function handleCreateUser(userData) {
    setIsLoading(true);
    try {
      // Set role mặc định là "normal" vì không có field role trong form
      const userDataWithRole = {
        ...userData,
        role: "normal"
      };
      const apiResponse = await createUser(userDataWithRole);
      if (apiResponse) {
        toast.success(t("CREATE_USER_SUCCESS"));
        setShowCreateModal(false);
        fetchUsersData();
      }
    } catch (error) {
      toast.error(t("CREATE_USER_ERROR"));
    } finally {
      setIsLoading(false);
    }
  }

  async function handleUpdateUser(userId, userData) {
    setIsLoading(true);
    try {
      // Loại bỏ role khỏi userData để không thay đổi role hiện tại
      const { role, ...userDataWithoutRole } = userData;
      const apiResponse = await updateUserInfo(userId, userDataWithoutRole);
      if (apiResponse) {
        toast.success(t("UPDATE_USER_SUCCESS"));
        setShowEditModal(false);
        setEditingUser(null);
        fetchUsersData();
      }
    } catch (error) {
      toast.error(t("UPDATE_USER_ERROR"));
    } finally {
      setIsLoading(false);
    }
  }

  function handleEditUser(user) {
    setEditingUser(user);
    setShowEditModal(true);
  }

  const onSubmitFilter = (values) => {
    // Lọc bỏ các giá trị rỗng, null, undefined
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      const value = values[key];
      if (value !== null && value !== undefined && value !== '') {
        acc[key] = value;
      }
      return acc;
    }, {});

    handleReplaceUrlSearch(1, usersData.paging.pageSize, filteredValues);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, usersData.paging.pageSize, {});
  };

  const columns = [
    orderColumn(usersData.paging),
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      width: 200,
      render: (text, record) => (
        <div className="user-name-cell">
          <div className="user-name">{text}</div>
          <div className="user-email">{record.email}</div>
        </div>
      ),
    },
    {
      title: t("PHONE"),
      dataIndex: "phone",
      width: 120,
      render: (phone) => phone || "-"
    },
    {
      title: t("TYPE"),
      dataIndex: "type",
      width: 100,
      align: "center",
      render: (type) => {
        const typeConfig = {
          student: { color: "blue", text: t("STUDENT") },
          teacher: { color: "orange", text: t("ADMIN") }
        };
        const config = typeConfig[type] || { color: "default", text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: t("STATUS"),
      dataIndex: "active",
      width: 100,
      align: "center",
      render: (active) => (
        <Tag color={active ? "green" : "red"}>
          {active ? t("ACTIVE") : t("INACTIVE")}
        </Tag>
      )
    },
    {
      title: t("LAST_LOGIN"),
      dataIndex: "lastLogin",
      width: 150,
      render: (lastLogin) => lastLogin ? formatDate(lastLogin) : "-"
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      width: 150,
      render: (createdAt) => formatDate(createdAt)
    },
    {
      title: t("ACTION"),
      width: 100,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <div className="action-buttons">
          <Tooltip title={t("EDIT_USER")}>
            <div
              className="action-btn action-btn-edit"
              onClick={() => handleEditUser(record)}
            >
              <EditOutlined />
            </div>
          </Tooltip>
          <Tooltip title={t("DELETE_USER")}>
            <div
              className="action-btn action-btn-delete"
              onClick={() => handleDeleteUser(record._id, record.fullName)}
            >
              <DeleteIcon />
            </div>
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <Loading active={isLoading} transparent>
      <div className="user-management-container">
        <Card className="user-management-info-card">
          <div className="user-management-info-header">
            <div>
              <h1 className="user-management-title">{t("USER_MANAGEMENT")}</h1>
              <p className="user-management-description">{t("USER_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size="large"
              className="btn-create-user"
              icon={<PlusOutlined />}
              onClick={() => setShowCreateModal(true)}
            >
              {t("CREATE_USER")}
            </AntButton>
          </div>
        </Card>

        <Card className="user-management-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={18}>
                <Row gutter={16}>
                  <Col xs={24} md={8}>
                    <AntForm.Item name="email" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_EMAIL_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={5}>
                    <AntForm.Item name="type" className="search-form-item">
                      <Select
                        options={TYPE_OPTIONS}
                        allowClear
                        placeholder={t("FILTER_BY_TYPE")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={6}>
                    <AntForm.Item name="active" className="search-form-item">
                      <Select
                        options={STATUS_OPTIONS}
                        allowClear
                        placeholder={t("FILTER_BY_STATUS")}
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={6} className="search-buttons-col">
                <div className="admin-filter-buttons">
                  <AntButton
                    type={BUTTON.GHOST_WHITE}
                    size="large"
                    onClick={onClearFilter}
                  >
                    {t("CLEAR_FILTER")}
                  </AntButton>
                  <AntButton
                    type={BUTTON.DEEP_NAVY}
                    size="large"
                    htmlType="submit"
                  >
                    {t("SEARCH_FILTER")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="user-management-table-card">
          <TableAdmin
            scroll={{ x: 1400 }}
            columns={columns}
            dataSource={usersData.rows}
            loading={isLoading}
            pagination={paginationConfig(usersData.paging, usersData.query)}
            rowKey="_id"
          />
        </Card>
      </div>

      {/* Create User Modal */}
      <CreateUserModal
        visible={isShowCreateModal}
        onCancel={() => setShowCreateModal(false)}
        onFinish={handleCreateUser}
        loading={isLoading}
      />

      {/* Edit User Modal */}
      <EditUserModal
        visible={isShowEditModal}
        onCancel={() => {
          setShowEditModal(false);
          setEditingUser(null);
        }}
        onFinish={handleUpdateUser}
        userData={editingUser}
        loading={isLoading}
      />
    </Loading>
  );
};

export default UserManagementScreen;
