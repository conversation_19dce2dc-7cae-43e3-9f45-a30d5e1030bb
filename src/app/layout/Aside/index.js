import { useEffect, useMemo } from "react";
import { Layout } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { connect } from "react-redux";

import MainAside from "@app/layout/Aside/MainAside";
import AdminAside from "@app/layout/Aside/AdminAside";
import { hasAdminAccess } from "@src/utils/adminPermissions";

import { LINK } from "@link";

import Logo_sacombank from "@src/asset/logo/Logo-sacombank.svg";


import "./Aside.scss";

function Aside({  user, ...props }) {
  const { pathname } = useLocation();

  const isShowAdminAside = useMemo(() => hasAdminAccess(user) && pathname.includes(LINK.ADMIN_PAGE), [pathname, user]);

  // Determine logo redirect URL based on user role
  const logoRedirectUrl = useMemo(() => {
    return hasAdminAccess(user) ? LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT : LINK.HOMEPAGE;
  }, [user]);

  useEffect(() => {

  }, []);

  if ([LINK.SUBSCRIPTION, LINK.PAYMENT_ID.format(":id"), LINK.PAYMENT_VNPAY].some((item) => matchPath(item, pathname))) return null;

  return <Layout.Sider id="aside" width={264}>
    <div className="aside-header">
      <Link to={logoRedirectUrl}>
        <img className="aside-header__logo" src={Logo_sacombank} alt="" />
      </Link>
    </div>

    {isShowAdminAside ? <AdminAside /> : <MainAside />}

  </Layout.Sider>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}



export default (connect(mapStateToProps)(Aside));
