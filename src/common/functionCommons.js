import React from "react";
import Cookies from "js-cookie";
import jwtDecode from "jwt-decode";
import _, { isEqual, isObject, kebabCase, transform } from "lodash";
import queryString from "query-string";
import { t } from "i18next";
import dayjs from "dayjs";
import { PDFDocument } from "pdf-lib";

import {
  CONSTANT,
  GENERAL_ACCESS_TYPE,
  LANGUAGE,
  PAGINATION_CONFIG,
  PAGINATION_INIT,
  PERMISSION,
} from "@src/constants/constant";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";
import { getNavigate } from "@navigation";


import FILE_DOC from "@src/asset/icon/file/file-doc.svg";
import FILE_DOCX from "@src/asset/icon/file/file-docx.svg";
import FILE_JPG from "@src/asset/icon/file/file-jpg.svg";
import FILE_MP3 from "@src/asset/icon/file/file-mp3.svg";
import FILE_MP4 from "@src/asset/icon/file/file-mp4.svg";
import FILE_PDF from "@src/asset/icon/file/file-pdf.svg";
import FILE_PNG from "@src/asset/icon/file/file-png.svg";
import FILE_MOV from "@src/asset/icon/file/file-mov.svg";
import FILE_WAV from "@src/asset/icon/file/file-wav.svg";
import FILE_BLUE from "@src/asset/icon/file/file-blue.svg";

const TIME_FORMAT = {
  TIME: "HH:mm",
  DATE: "DD/MM/YYYY",
};

export function cloneObj(input = {}) {
  return JSON.parse(JSON.stringify(input));
}

export function setCookieTheme(themeInput) {
  Cookies.set("theme", themeInput, { expires: 1000 });
}

export function joinClass(arrClass = []) {
  return arrClass.filter(x => !!x).join(" ");
}

export function handleReplaceUrlSearch(page, limit, query) {
  const navigate = getNavigate();
  const queryObj = cloneObj(query);
  delete queryObj.page;
  delete queryObj.limit;
  let search = "";
  if (page || page === 0) {
    search += search ? "&" : "";
    search += `page=${page}`;
  }
  if (limit || limit === 0) {
    search += search ? "&" : "";
    search += `limit=${limit}`;
  }
  if (Object.values(queryObj).length) {
    search += search.length > 1 ? "&" : "";
    search += convertObjectToQuery(queryObj);
  }
  navigate(`?${search}`, { replace: true });
  // history.replace({ search });
}

export function calPagingAfterDelete({ rows, paging }) {
  if (!Array.isArray(rows) || !paging.page || paging.page === 1) {
    paging.page = 1;
  }
  paging.page = rows.length === 1 ? paging.page - 1 : paging.page;
  return paging;
}

export function navigateAfterDelete({ rows, paging, query }) {
  let page;
  if (!Array.isArray(rows) || !paging.page || paging.page === 1) {
    page = 1;
  } else {
    page = rows.length === 1 ? paging.page - 1 : paging.page;
  }
  replaceLocationQuery({ ...paging, page }, query);
}

export function replaceLocationQuery(paging, query) {
  const navigate = getNavigate();
  const { page, pageSize } = paging;

  const queryObj = cloneObj(query);
  delete queryObj.page;
  delete queryObj.pageSize;
  let search = "";
  if (page || page === 0) {
    search += search ? "&" : "";
    search += `page=${page}`;
  }
  if (pageSize || pageSize === 0) {
    search += search ? "&" : "";
    search += `limit=${pageSize}`;
  }
  if (Object.values(queryObj).length) {
    search += search.length > 1 ? "&" : "";
    search += convertObjectToQuery(queryObj);
  }
  navigate(`?${search}`, { replace: true });
}

export function convertObjectToQuery(queryObj) {
  let query = "";
  const sortable = Object.fromEntries(
    Object.entries(queryObj).sort(([, a], [, b]) => a - b),
  );
  Object.entries(sortable).forEach(([key, value]) => {
    // Chỉ thêm vào query nếu value không rỗng
    if (value !== null && value !== undefined && value !== '') {
      query += query ? "&" : "";
      query += `${kebabCase(key)}=${encodeURIComponent(value)}`;
    }
  });
  return query;
}

export function handleSearchParams(queryStr) {

  const query = convertSnakeCaseToCamelCase(queryString.parseUrl(queryStr).query);
  const paging = {
    page: query.page || PAGINATION_INIT.paging.page,
    pageSize: query.limit || PAGINATION_INIT.paging.pageSize,
  };
  delete query.page;
  delete query.limit;

  return { paging, query };
}


export function convertObjectToParam(queryObj, firstCharacter = "?") {

  function renderQuery(queryInput, queryAdd, firstCharacter) {
    let queryOutput = queryInput ? "&" : firstCharacter;
    queryOutput += queryAdd;
    return queryInput + queryOutput;
  }

  if (typeof queryObj !== "object") return "";

  let query = "";
  const sortable = Object.fromEntries(
    Object.entries(queryObj).sort(([, a], [, b]) => a - b),
  );

  Object.entries(sortable).forEach(([key, value]) => {
    if (value) {
      query = renderQuery(query, `${key}=${value}`, firstCharacter);
    }
  });
  return query;
}

export function genQueryParam(queryObj = {}) {
  if (typeof queryObj !== "object" || !Object.keys(queryObj).length) return "";
  const queryArr = [];
  if (queryObj.hasOwnProperty("sort")) {
    queryArr.push(`sort=${queryObj.sort}`);
    delete queryObj.sort;
  }
  if (queryObj.hasOwnProperty("limit")) {
    queryArr.push(`limit=${queryObj.limit}`);
    delete queryObj.limit;
  }

  Object.entries(queryObj).forEach(([key, value]) => {
    if (!value) {
      delete queryObj[key];
    } else {
      queryObj[key] = encodeURIComponent(value);
    }
  });

  if (Object.keys(queryObj).length) {
    queryArr.push(`query=${JSON.stringify(queryObj)}`);
  }

  return queryArr.reverse().join("&");
}

export function genPopulateParam(populateOpts = []) {
  if (!populateOpts?.length) return "";
  return `populate=${populateOpts.join(",")}`;
}

export function genSearchFieldParam(searchFields = []) {
  if (!searchFields?.length) return "";
  return `searchFields=${searchFields.join(",")}`;
}


export function convertFileName(str) {
  if (!str) return "";

  str = str.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a");
  str = str.replace(/[èéẹẻẽêềếệểễ]/g, "e");
  str = str.replace(/[ìíịỉĩ]/g, "i");
  str = str.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o");
  str = str.replace(/[ùúụủũưừứựửữ]/g, "u");
  str = str.replace(/[ỳýỵỷỹ]/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]/g, "A");
  str = str.replace(/[ÈÉẸẺẼÊỀẾỆỂỄ]/g, "E");
  str = str.replace(/[ÌÍỊỈĨ]/g, "I");
  str = str.replace(/[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]/g, "O");
  str = str.replace(/[ÙÚỤỦŨƯỪỨỰỬỮ]/g, "U");
  str = str.replace(/[ỲÝỴỶỸ]/g, "Y");
  str = str.replace(/Đ/g, "D");
  str = str.replace(/\s+/g, " ");
  str.trim();
  return str;
}

export function cleanFileName(str) {
  if (!str) return "";
  str = str.replace(/[^a-zA-Z0-9\s._-àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/g, "");
  str = str.replace(/\s+/g, " ");
  str = str.trim();
  str = str.replace(/\//g, "");

  if (str.length > 20) {
    str = str.substring(0, 20);
  }
  return str;
}

export function findMax(data) {
  if (!Array.isArray(data) || !data.length) return null;
  let max = typeof data[0] === "number"
    ? data[0]
    : Array.isArray(data[0]) && data[0][0] ? data[0][0] : 0;
  data.forEach(item => {
    if (typeof item === "number") {
      max = max < item ? item : max;
    }
    if (Array.isArray(item)) {
      item.forEach(itemChild => {
        max = max < itemChild ? itemChild : max;
      });
    }
  });
  return max;
}

export function randomKey() {
  return Math.floor(Math.random() * 100000000000);
}

export function checkTokenExp(authToken) {
  if (!authToken) return;
  try {
    const exp = jwtDecode(authToken).exp;
    const now = Date.now().valueOf() / 1000;
    return now < exp;
  } catch (e) {
    return null;
  }
}

export function hexToRgb(hex) {
  if (!hex) return null;
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  const r = parseInt(result[1], 16);
  const g = parseInt(result[2], 16);
  const b = parseInt(result[3], 16);
  return result ? `rgb(${r}, ${g}, ${b})` : null;
}

export function difference(object, base) {
  return transform(object, (result, value, key) => {
    if (!isEqual(value, base[key])) {
      result[key] = isObject(value) && isObject(base[key]) ? difference(value, base[key]) : value;
    }
  });
}

export function dateToISOString(date) {
  try {
    return date ? date.toISOString() : "";
  } catch (e) {
    return null;
  }
}

export function formatDate(dateTime) {
  try {
    if (dayjsValid(dateTime)) {
      return dayjs(dateTime).format(TIME_FORMAT.DATE);
    }
  } catch (e) {
    return null;
  }
}

export function formatDateTime(dateTime) {
  try {
    if (dayjsValid(dateTime)) {
      const space = " ";
      const formatString = TIME_FORMAT.DATE + space + TIME_FORMAT.TIME;
      return dayjs(dateTime).format(formatString);
    }
  } catch (e) {
    return null;
  }
}

export function formatTimeDate(dateTime, showComma = true) {
  try {
    if (dayjsValid(dateTime)) {
      const space = showComma ? ", " : " ";
      const formatString = TIME_FORMAT.TIME + space + TIME_FORMAT.DATE;
      return dayjs(dateTime).format(formatString);
    }
  } catch (e) {
    return null;
  }
}

export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

//--------------------------------------------------------------------


export function formatUnique(arr) {
  return Array.from(new Set(arr)); //
}

export function checkLoaded() {
  return document.readyState === "complete";
}


function replaceCommaToDot(input) {
  if (!input) return input;
  const stringInput = input.toString();
  return stringInput.replace(",", ".");
}

export const getFileExtension = (filename) => {
  let ext = /^.+\.([^.]+)$/.exec(filename);
  return ext === null ? "" : ext[1]?.toLowerCase();
};

export function removeFileExtension(filename) {
  const lastDotIndex = filename.lastIndexOf(".");
  return lastDotIndex !== -1 ? filename.slice(0, lastDotIndex) : filename;
}

export function isNumeric(n) {
  return !isNaN(parseFloat(n)) && isFinite(n);
}

export const toAbsoluteUrl = (pathname) => process.env.PUBLIC_URL + pathname;

export function orderColumn(paging = {}, onCell) {
  if (!paging || typeof paging !== "object" || !Object.keys(paging).length) return null;

  const { page, pageSize } = paging;
  return {
    title: t("ORDER"), dataIndex: "inspectionType", align: CONSTANT.CENTER, width: 80,
    render: (_, __, index) => (page - 1) * pageSize + index + 1,
    onCell: onCell,
  };
}

export function handleDataSourcePaging(inputData, pagingData) {
  if (!Array.isArray(inputData)) return [];
  const indexStart = (pagingData.page - 1) * pagingData.pageSize;
  const indexEnd = indexStart + pagingData.pageSize;
  return _.slice(inputData, indexStart, indexEnd);
}

export function handlePageWhenDelete(paging = {}, arrayData = [], setPaging) {
  if (paging.page > 1 && arrayData.length === (paging.page - 1) * paging.pageSize) {
    setPaging({ ...paging, page: paging.page - 1 });
  }
}

export function dayjsValid(date) {
  return date && dayjs(date).isValid();
}

export function convertDateToText(dateTime) {
  if (dayjs.locale() === LANGUAGE.EN) {
    return dayjs(dateTime).locale(LANGUAGE.EN).format("MMMM DD, YYYY");
  }
  return dayjs(dateTime).locale(LANGUAGE.VI).format("DD MMMM, YYYY");
}

export function convertHHMMSSToSecond(stringTime) {
  try {
    const [hour, minute, second] = stringTime.split(":");
    return +hour * 60 * 60 + (+minute) * 60 + (+second);
  } catch (e) {
    return null;
  }
}

export function convertSecondToHHMMSS(seconds) {
  try {
    const { formattedHours, formattedMinutes, formattedSeconds } = getHMS(seconds);
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  } catch (e) {
    return null;
  }
}

export function convertSecondToMs(seconds) {
  try {
    const { formattedMinutes, formattedSeconds } = getHMS(seconds);
    return `${formattedMinutes}m${formattedSeconds}s`;
  } catch (e) {
    return "00m00s";
  }
}

export function convertSecondToHMS(seconds) {
  try {
    const { formattedMinutes, formattedSeconds } = getHMS(seconds);
    return `${formattedMinutes}m${formattedSeconds}s`;
  } catch (e) {
    return null;
  }
}

export function getHMS(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(remainingSeconds).padStart(2, "0");
  return { formattedHours, formattedMinutes, formattedSeconds };
}

export function getYoutubeVideoId(url) {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url?.match(regExp);

  return (match && match[2].length === 11)
    ? match[2]
    : null;
}

export function stringSplit(stringInput) {
  return stringInput.replace(/\s+/g, " ").split(" ").filter(x => !!x);
}

export function stringSplitWithLineBreak(stringInput) {
  return stringInput.replace(/\s+/g, " ").replace(/(<br\s*\/?>)+/gi, " ").split(" ").filter(x => !!x);
}

export function timeIsValid(timeInput) {
  return /^([0-1]?\d|2[0-3])(?::([0-5]?\d))?(?::([0-5]?\d))?$/.test(timeInput);
}

export function renderPlainTextWithLineBreaks(text) {
  if (!text) return;
  return text.split("\n").reduce((total, line) => [total, <br key={line} />, line]);
}

export function paginationConfig(paging, query, lang = "en", paginationConfig = PAGINATION_CONFIG) {

  const pagination = Object.assign({}, paginationConfig);
  pagination.showTotal = (total, range) => `${range[0]}-${range[1]} ${lang === "en" ? "of" : "của"} ${total}`;
  pagination.onChange = (page, pageSize) => handleReplaceUrlSearch(page, pageSize, query);
  if (paging) {
    pagination.current = paging.page;
    pagination.total = paging.total;
    pagination.pageSize = paging.pageSize;
  }
  return pagination;
}

export function convertQueryToObject(queryStr) {
  return convertSnakeCaseToCamelCase(queryString.parseUrl(queryStr).query);
}

export function generateClassNameByCharacter(character) {
  if (!character) return "";
  const upperCaseChar = convertFileName(character).toUpperCase();
  let className = "yellow-bg";
  if (upperCaseChar >= "A" && upperCaseChar <= "G") className = "blue-bg";
  else if (upperCaseChar >= "H" && upperCaseChar <= "N") className = "green-bg";
  else if (upperCaseChar >= "O" && upperCaseChar <= "U") className = "purple-bg";
  return className;
}

export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const userPermissonFromGeneralAccess = (generalAccess) => {
  if (generalAccess.typeAccess === GENERAL_ACCESS_TYPE.RESTRICTED) {
    return PERMISSION.NO_PERMISSION;
  }
  return generalAccess.permission;
};

const userPermissionFromUserAccess = (userAccess = [], user) => {
  if (userAccess[0].userId?._id === user._id) {
    return PERMISSION.EDITOR;
  }
  const access = userAccess.slice(1).find((access) => {
    return !access.isDeleted && access.userId?._id === user._id;
  });
  return access?.permission || PERMISSION.NO_PERMISSION;
};

export function generatePermissionFromAccess(userAccess, generalAccess, user) {
  const userPermission = [userPermissionFromUserAccess(userAccess, user), userPermissonFromGeneralAccess(generalAccess)];
  if (userPermission.includes(PERMISSION.EDITOR)) {
    return PERMISSION.EDITOR;
  }
  if (userPermission.includes(PERMISSION.VIEWER)) {
    return PERMISSION.VIEWER;
  }
  return PERMISSION.NO_PERMISSION;
}

export function calGoogleWindowPosition(w = 600, h = 600) {
  const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
  const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

  const windowWidth = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
  const windowHeight = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

  const systemZoom = windowWidth / window.screen.availWidth;
  const left = (windowWidth - w) / 2 / systemZoom + dualScreenLeft;
  const top = (windowHeight - h) / 2 / systemZoom + dualScreenTop;
  const width = w / systemZoom;
  const height = h / systemZoom;
  return { left, top, width, height };
}

export function renderMoney(amount) {
  let moneyNumber = 0;
  const configValue = new Intl.NumberFormat("de-DE");
  if (amount) {
    moneyNumber = configValue.format(amount);
  }
  return `${moneyNumber} ${t("VND")}`;
}

export function checkFileType(file) {
  // Get the file extension
  const extension = file.name.split(".").pop().toLowerCase();

  // Example: Check if the file type is an image
  if (file.type.startsWith("image/") || ["jpg", "jpeg", "png", "gif", "bmp"].includes(extension)) {
    return CONSTANT.IMAGE;
  }

  // Example: Check if the file type is a PDF
  if (file.type === "application/pdf" || extension === "pdf") {
    return CONSTANT.PDF;
  }

  // Add more checks for other file types as needed

  // Default case
  return undefined;
}

/**
 * Format bytes as human-readable text.
 *
 * @param bytes Number of bytes.
 * @param si True to use metric (SI) units, aka powers of 1000. False to use
 *           binary (IEC), aka powers of 1024.
 * @param dp Number of decimal places to display.
 *
 * @return Formatted string.
 */
export function humanFileSize(bytes, si = false, dp = 2) {
  const thresh = si ? 1000 : 1024;

  if (Math.abs(bytes) < thresh) {
    return bytes + " B";
  }

  //const units = si
  //  ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  //  : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];

  const units = ["kB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  let u = -1;
  const r = 10 ** dp;

  do {
    bytes /= thresh;
    ++u;
  } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);


  return bytes.fixedFloat(dp) + " " + units[u];
}

export const coverLanguageObjectToArray = (obj = {}) => {
  if (!Object.keys(obj).length) return [];

  const result = [];
  const langs = new Set();


  for (const key in obj) {
    const langObj = obj[key];
    for (const lang in langObj) {
      langs.add(lang);
    }
  }


  for (const lang of langs) {
    const langObj = {};
    langObj.lang = lang;
    for (const key in obj) {
      if (obj[key][lang]) {
        langObj[key] = obj[key][lang];
      }
    }
    result.push(langObj);
  }

  return result;
};

export const coverLangArrayToObject = (arr = []) => {
  if (!arr.length) return null;
  return arr.reduce((toObject, item) => {
    if (!item) return toObject;
    const keyObj = Object.keys(item);
    keyObj.map((keyItem) => {
      if (!toObject[keyItem]) {
        toObject[keyItem] = {};
      }
      toObject[keyItem][item.lang] = item[keyItem];
    });

    return toObject;
  }, {});
};

export function fileExtImage(file) {
  const extension = getFileExtension(file?.name);
  const imageMap = {
    doc: <img src={FILE_DOC} alt="file-doc" />,
    docx: <img src={FILE_DOCX} alt="file-docx" />,
    jpg: <img src={FILE_JPG} alt="file-jpg" />,
    mp3: <img src={FILE_MP3} alt="file-mp3" />,
    mp4: <img src={FILE_MP4} alt="file-mp4" />,
    pdf: <img src={FILE_PDF} alt="file-pdf" />,
    png: <img src={FILE_PNG} alt="file-png" />,
    mov: <img src={FILE_MOV} alt="file-mov" />,
    wav: <img src={FILE_WAV} alt="file-wav" />,
  };
  // Return the image component for the file extension or a default image component
  return imageMap[extension] || <img src={FILE_BLUE} alt="file" />;
}

export function removeTags(str) {
  if ((str === null) || (str === ""))
    return "";
  else
    str = str.toString();
  // Regular expression to identify HTML tags in
  // the input string. Replacing the identified
  // HTML tag with a null string.
  return str.replace(/(<([^>]+)>)/ig, "");
}

export const downloadUsingBrowser = (url, fileName) => {
  const link = document.createElement("a");
  link.href = url;
  //link.download = `${fileName}.docx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function getNumberOfPages(file) {
  const arrayBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(arrayBuffer);
  return pdfDoc.getPageCount();
}

export function checkMediaExistInHtml(htmlString) {
  // Sử dụng DOMParser để chuyển đổi chuỗi HTML thành tài liệu DOM
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");

  // Kiểm tra các thẻ hình ảnh, video, audio, liên kết
  const hasImage = !!doc.querySelector("img");
  const hasVideo = !!doc.querySelector("video");
  const hasAudio = !!doc.querySelector("audio");
  const iframes = doc.querySelectorAll("iframe");
  const links = doc.querySelectorAll("a");

  // Kiểm tra xem có video YouTube qua iframe không
  let hasYouTubeVideo = false;
  iframes.forEach(iframe => {
    const src = iframe.getAttribute("src");
    if (src && (src.includes("youtube.com") || src.includes("youtu.be"))) {
      hasYouTubeVideo = true;
    }
  });

  // Kiểm tra xem có link chứa hình ảnh, video, audio hoặc YouTube video không
  let linkContainsMedia = false;
  links.forEach(link => {
    const href = link.getAttribute("href");
    if (href && (href.includes("youtube.com") || href.includes("youtu.be"))) {
      hasYouTubeVideo = true; // Xem link có video YouTube hay không
    }
    if (link.querySelector("img") || link.querySelector("video") || link.querySelector("audio")) {
      linkContainsMedia = true; // Xem link có chứa media khác hay không
    }
  });

  // Kiểm tra có media trong thẻ figure không
  const figures = doc.querySelectorAll("figure");
  let hasMediaInFigure = false;
  let mediaTypesInFigure = {
    image: false,
    video: false,
    audio: false,
    oembed: false, // Thêm kiểm tra cho oembed
  };

  figures.forEach(figure => {
    if (figure.querySelector("img")) {
      mediaTypesInFigure.image = true; // Có hình ảnh trong figure
    }
    if (figure.querySelector("video")) {
      mediaTypesInFigure.video = true; // Có video trong figure
    }
    if (figure.querySelector("audio")) {
      mediaTypesInFigure.audio = true; // Có audio trong figure
    }
    if (figure.querySelector("oembed")) {
      mediaTypesInFigure.oembed = true; // Có thẻ oembed trong figure
      const oembed = figure.querySelector("oembed");
      const url = oembed.getAttribute("url");
      // Kiểm tra xem oembed có phải là video YouTube không
      if (url && (url.includes("youtube.com") || url.includes("youtu.be"))) {
        mediaTypesInFigure.video = true; // Đánh dấu là có video YouTube
      }
    }

    // Kiểm tra tổng thể có media trong thẻ figure
    if (mediaTypesInFigure.image || mediaTypesInFigure.video || mediaTypesInFigure.audio || mediaTypesInFigure.oembed) {
      hasMediaInFigure = true; // Nếu có bất kỳ media nào trong figure
    }
  });

  // Tạo đối tượng kết quả trả về
  return hasImage || hasVideo || hasAudio || hasYouTubeVideo || linkContainsMedia || hasMediaInFigure
    || mediaTypesInFigure.image || mediaTypesInFigure.video || mediaTypesInFigure.audio || mediaTypesInFigure.oembed;
}

export function stringToBool(str) {
  if (!str) return false;
  return str.toLowerCase() === "true";
}

export const getColumnSortOrder = (column, queryParams) => {
  const { sort } = queryParams || {};
  const isDescending = sort?.startsWith("-");
  const sortedColumn = isDescending ? sort?.replace("-", "") : sort;

  return sortedColumn === column ? (isDescending ? "descend" : "ascend") : null;
};

export function secondsToHMS(seconds) {
  const roundedSeconds = Math.round(seconds);

  const hours = Math.floor(roundedSeconds / 3600);
  const minutes = Math.floor((roundedSeconds % 3600) / 60);
  const secs = roundedSeconds % 60;

  // Đảm bảo định dạng 2 chữ số
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(secs).padStart(2, "0");

  return {
    hours: formattedHours,
    minutes: formattedMinutes,
    seconds: formattedSeconds,
  };
}

export function renderAudioDuration(time) {
  try {
    const { minutes, seconds } = secondsToHMS(time);
    return `${minutes}:${seconds}`;
  } catch (e) {
    return "00:00";
  }
}

export function calculateIELTSOverall(scores = []) {
  const total = scores.reduce((sum, score) => sum + score, 0);

  const average = total / scores.length;

  return Math.round(average * 2) / 2;
}

export const renderTextTime = (fator, text, language) => {
  if (language === 'en' && fator > 1) return `${fator} ${text}s`;
  return `${fator} ${text}`
}
